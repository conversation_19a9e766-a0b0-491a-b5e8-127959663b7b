2025-07-22 14:01:19 - payment_intent_created - {"payment_intent_id":"pi_3RnesjRFYW7g8VJ21xXCrx6W","amount":225,"currency":"USD"}
2025-07-22 14:10:37 - customer_created - {"customer_id":"cus_Sj7GFGuPILnc9V","email":"<EMAIL>"}
2025-07-22 14:10:38 - payment_intent_updated - {"payment_intent_id":"pi_3RnesjRFYW7g8VJ21xXCrx6W","customer_id":"cus_Sj7GFGuPILnc9V"}
2025-07-22 14:13:19 - payment_intent_created - {"payment_intent_id":"pi_3Rnf4LRFYW7g8VJ21uMJTStZ","amount":150,"currency":"USD"}
2025-07-22 14:14:02 - customer_created - {"customer_id":"cus_Sj7JQvEd8S7990","email":"<EMAIL>"}
2025-07-22 14:14:04 - payment_intent_updated - {"payment_intent_id":"pi_3Rnf4LRFYW7g8VJ21uMJTStZ","customer_id":"cus_Sj7JQvEd8S7990"}
2025-07-22 14:17:03 - payment_intent_created - {"payment_intent_id":"pi_3Rnf7xRFYW7g8VJ21Gm6Wocv","amount":150,"currency":"USD"}
2025-07-22 14:18:56 - customer_created - {"customer_id":"cus_Sj7O2ZarqAiDWW","email":"<EMAIL>"}
2025-07-22 14:18:57 - payment_intent_updated - {"payment_intent_id":"pi_3Rnf7xRFYW7g8VJ21Gm6Wocv","customer_id":"cus_Sj7O2ZarqAiDWW"}
2025-07-22 14:23:01 - payment_intent_created - {"payment_intent_id":"pi_3RnfDjRFYW7g8VJ20IJsX6RL","amount":150,"currency":"USD"}
2025-07-22 14:23:46 - customer_created - {"customer_id":"cus_Sj7TgaBvAku5TR","email":"<EMAIL>"}
2025-07-22 14:23:47 - payment_intent_updated - {"payment_intent_id":"pi_3RnfDjRFYW7g8VJ20IJsX6RL","customer_id":"cus_Sj7TgaBvAku5TR"}
2025-07-22 14:29:51 - payment_intent_created - {"payment_intent_id":"pi_3RnfKLRFYW7g8VJ2144xVxT0","amount":150,"currency":"USD"}
2025-07-22 14:30:31 - customer_created - {"customer_id":"cus_Sj7aPQDiVo0Xcs","email":"<EMAIL>"}
2025-07-22 14:30:33 - payment_intent_updated - {"payment_intent_id":"pi_3RnfKLRFYW7g8VJ2144xVxT0","customer_id":"cus_Sj7aPQDiVo0Xcs"}
2025-07-22 14:46:00 - payment_intent_created - {"payment_intent_id":"pi_3RnfZyRFYW7g8VJ20BIDCF37","amount":225,"currency":"USD"}
2025-07-22 14:46:43 - customer_created - {"customer_id":"cus_Sj7q0dn5ar4UeP","email":"<EMAIL>"}
2025-07-22 14:46:44 - payment_intent_updated - {"payment_intent_id":"pi_3RnfZyRFYW7g8VJ20BIDCF37","customer_id":"cus_Sj7q0dn5ar4UeP"}
2025-07-23 01:28:32 - payment_intent_created - {"payment_intent_id":"pi_3RnpbnRFYW7g8VJ20L3hnWc8","amount":225,"currency":"USD"}
2025-07-23 01:29:32 - customer_created - {"customer_id":"cus_SjIDM2cl9bnUp9","email":"<EMAIL>"}
2025-07-23 01:29:34 - payment_intent_updated - {"payment_intent_id":"pi_3RnpbnRFYW7g8VJ20L3hnWc8","customer_id":"cus_SjIDM2cl9bnUp9"}
2025-07-23 01:32:41 - payment_intent_created - {"payment_intent_id":"pi_3RnpfpRFYW7g8VJ21ktPeoZw","amount":375,"currency":"USD"}
2025-07-23 01:33:35 - customer_created - {"customer_id":"cus_SjIHsKouKiwkz0","email":"<EMAIL>"}
2025-07-23 01:33:37 - payment_intent_updated - {"payment_intent_id":"pi_3RnpfpRFYW7g8VJ21ktPeoZw","customer_id":"cus_SjIHsKouKiwkz0"}
2025-07-23 01:40:18 - payment_intent_created - {"payment_intent_id":"pi_3Rnpn5RFYW7g8VJ21PXzi5YE","amount":300,"currency":"USD"}
2025-07-23 01:42:07 - customer_created - {"customer_id":"cus_SjIPdLuKW7mDnm","email":"<EMAIL>"}
2025-07-23 01:42:09 - payment_intent_updated - {"payment_intent_id":"pi_3Rnpn5RFYW7g8VJ21PXzi5YE","customer_id":"cus_SjIPdLuKW7mDnm"}
2025-07-23 05:03:57 - payment_intent_created - {"payment_intent_id":"pi_3RnsyCRFYW7g8VJ21PBhplIq","amount":300,"currency":"USD"}
2025-07-23 05:04:54 - customer_created - {"customer_id":"cus_SjLgT4OfOi7Hn4","email":"<EMAIL>"}
2025-07-23 05:04:56 - payment_intent_updated - {"payment_intent_id":"pi_3RnsyCRFYW7g8VJ21PBhplIq","customer_id":"cus_SjLgT4OfOi7Hn4"}
2025-07-23 06:54:59 - payment_intent_created - {"payment_intent_id":"pi_3RnuhjRFYW7g8VJ219bxkcn3","amount":225,"currency":"USD"}
2025-07-23 06:55:52 - customer_created - {"customer_id":"cus_SjNTcKFikeP32i","email":"<EMAIL>"}
2025-07-23 06:55:55 - payment_intent_updated - {"payment_intent_id":"pi_3RnuhjRFYW7g8VJ219bxkcn3","customer_id":"cus_SjNTcKFikeP32i"}
2025-07-23 12:42:43 - payment_intent_created - {"payment_intent_id":"pi_3Ro08ERFYW7g8VJ21c8JwnZ1","amount":150,"currency":"USD"}
2025-07-23 12:43:49 - payment_intent_created - {"payment_intent_id":"pi_3Ro09IRFYW7g8VJ21TbfPqXE","amount":150,"currency":"USD"}
2025-07-23 13:33:10 - payment_intent_created - {"payment_intent_id":"pi_3Ro0v3RFYW7g8VJ20dxHwZLk","amount":150,"currency":"USD"}
2025-07-23 13:33:20 - customer_created - {"customer_id":"cus_SjTsmtbgj4grES","email":"<EMAIL>"}
2025-07-23 13:33:21 - payment_intent_updated - {"payment_intent_id":"pi_3Ro0v3RFYW7g8VJ20dxHwZLk","customer_id":"cus_SjTsmtbgj4grES"}
2025-07-23 13:33:56 - customer_created - {"customer_id":"cus_SjTtMVEYk8MsRU","email":"<EMAIL>"}
2025-07-23 13:33:57 - payment_intent_update_failed - {"error":"You cannot modify `customer` on a PaymentIntent once it already has been set. To fulfill a payment with a different Customer, cancel this PaymentIntent and create a new one.","payment_intent_id":"pi_3Ro0v3RFYW7g8VJ20dxHwZLk"}
2025-07-23 13:40:20 - customer_created - {"customer_id":"cus_SjTz7u1SqO2GUA","email":"<EMAIL>"}
2025-07-23 13:40:21 - payment_intent_update_failed - {"error":"You cannot modify `customer` on a PaymentIntent once it already has been set. To fulfill a payment with a different Customer, cancel this PaymentIntent and create a new one.","payment_intent_id":"pi_3Ro0v3RFYW7g8VJ20dxHwZLk"}
2025-07-23 13:41:52 - payment_intent_created - {"payment_intent_id":"pi_3Ro13TRFYW7g8VJ21xeZBOa4","amount":225,"currency":"USD"}
2025-07-23 13:42:18 - customer_created - {"customer_id":"cus_SjU1nxej3V0P0y","email":"<EMAIL>"}
2025-07-23 13:42:19 - payment_intent_updated - {"payment_intent_id":"pi_3Ro13TRFYW7g8VJ21xeZBOa4","customer_id":"cus_SjU1nxej3V0P0y"}
2025-07-23 14:08:00 - payment_intent_created - {"payment_intent_id":"pi_3Ro1SgRFYW7g8VJ20NTPvjg1","amount":150,"currency":"USD"}
2025-07-23 14:08:59 - customer_created - {"customer_id":"cus_SjUS9g9kqschxm","email":"<EMAIL>"}
2025-07-23 14:09:00 - payment_intent_updated - {"payment_intent_id":"pi_3Ro1SgRFYW7g8VJ20NTPvjg1","customer_id":"cus_SjUS9g9kqschxm"}
2025-07-24 05:19:44 - payment_intent_created - {"payment_intent_id":"pi_3RoFh3RFYW7g8VJ20bf68BrV","amount":150,"currency":"USD"}
2025-07-24 05:20:14 - customer_created - {"customer_id":"cus_Sjj9X53nHMkSj9","email":"<EMAIL>"}
2025-07-24 05:20:15 - payment_intent_updated - {"payment_intent_id":"pi_3RoFh3RFYW7g8VJ20bf68BrV","customer_id":"cus_Sjj9X53nHMkSj9"}
2025-07-27 12:00:28 - payment_intent_created - {"payment_intent_id":"pi_3RpRNQRFYW7g8VJ20JqcChvQ","amount":150,"currency":"USD"}
2025-07-27 12:03:37 - customer_created - {"customer_id":"cus_SkxL4SPagDnZHg","email":"<EMAIL>"}
2025-07-27 12:03:38 - payment_intent_updated - {"payment_intent_id":"pi_3RpRNQRFYW7g8VJ20JqcChvQ","customer_id":"cus_SkxL4SPagDnZHg"}
2025-07-27 12:21:13 - payment_intent_created - {"payment_intent_id":"pi_3RpRhcRFYW7g8VJ210L2c6wH","amount":225,"currency":"USD"}
2025-07-27 12:22:02 - customer_created - {"customer_id":"cus_Skxd4b0I7ZsFsb","email":"<EMAIL>"}
2025-07-27 12:22:04 - payment_intent_updated - {"payment_intent_id":"pi_3RpRhcRFYW7g8VJ210L2c6wH","customer_id":"cus_Skxd4b0I7ZsFsb"}
2025-07-27 12:33:50 - payment_intent_created - {"payment_intent_id":"pi_3RpRtpRFYW7g8VJ20QyKvn52","amount":375,"currency":"USD"}
2025-07-27 12:34:40 - customer_created - {"customer_id":"cus_Skxq06ZLXZPSsQ","email":"<EMAIL>"}
2025-07-27 12:34:41 - payment_intent_updated - {"payment_intent_id":"pi_3RpRtpRFYW7g8VJ20QyKvn52","customer_id":"cus_Skxq06ZLXZPSsQ"}
2025-07-27 13:05:08 - payment_intent_created - {"payment_intent_id":"pi_3RpSO7RFYW7g8VJ214LQ2Hup","amount":150,"currency":"USD"}
2025-07-27 13:07:55 - payment_intent_failed - {"error":"Could not connect to Stripe (https:\/\/api.stripe.com\/v1\/payment_intents).  Please check your internet connection and try again.  If this problem persists, you should check Stripe's service status at https:\/\/twitter.com\/stripestatus, or let us <NAME_EMAIL>.\n\n(Network error [errno 28]: SSL connection timeout)","amount":225,"currency":"USD"}
2025-07-27 13:12:04 - payment_intent_failed - {"error":"Could not connect to Stripe (https:\/\/api.stripe.com\/v1\/payment_intents).  Please check your internet connection and try again.  If this problem persists, you should check Stripe's service status at https:\/\/twitter.com\/stripestatus, or let us <NAME_EMAIL>.\n\n(Network error [errno 6]: Could not resolve host: api.stripe.com)","amount":225,"currency":"USD"}
2025-07-27 13:23:17 - payment_intent_created - {"payment_intent_id":"pi_3RpSfgRFYW7g8VJ20vpBtRmo","amount":225,"currency":"USD"}
2025-07-27 13:24:04 - customer_created - {"customer_id":"cus_SkydJKYJtqGzIA","email":"<EMAIL>"}
2025-07-27 13:24:08 - payment_intent_updated - {"payment_intent_id":"pi_3RpSfgRFYW7g8VJ20vpBtRmo","customer_id":"cus_SkydJKYJtqGzIA"}
2025-07-27 13:33:38 - payment_intent_created - {"payment_intent_id":"pi_3RpSphRFYW7g8VJ21VoKhhtW","amount":300,"currency":"USD"}
2025-07-27 13:33:59 - customer_created - {"customer_id":"cus_SkynETTg1PmEIk","email":"<EMAIL>"}
2025-07-27 13:34:00 - payment_intent_updated - {"payment_intent_id":"pi_3RpSphRFYW7g8VJ21VoKhhtW","customer_id":"cus_SkynETTg1PmEIk"}
2025-07-27 16:54:41 - payment_intent_created - {"payment_intent_id":"pi_3RpVy8RFYW7g8VJ20g5PHt32","amount":225,"currency":"USD"}
2025-07-27 16:55:02 - customer_created - {"customer_id":"cus_Sl22OIAVPLxoTK","email":"<EMAIL>"}
2025-07-27 16:55:03 - payment_intent_updated - {"payment_intent_id":"pi_3RpVy8RFYW7g8VJ20g5PHt32","customer_id":"cus_Sl22OIAVPLxoTK"}
2025-07-27 16:57:26 - payment_intent_created - {"payment_intent_id":"pi_3RpW0vRFYW7g8VJ20RLD4duz","amount":225,"currency":"USD"}
2025-07-27 16:57:52 - customer_created - {"customer_id":"cus_Sl25mnrI56M1nD","email":"<EMAIL>"}
2025-07-27 16:57:53 - payment_intent_updated - {"payment_intent_id":"pi_3RpW0vRFYW7g8VJ20RLD4duz","customer_id":"cus_Sl25mnrI56M1nD"}
2025-07-27 16:58:22 - customer_created - {"customer_id":"cus_Sl26VH4NE6OmxL","email":"<EMAIL>"}
2025-07-27 16:58:24 - payment_intent_update_failed - {"error":"You cannot modify `customer` on a PaymentIntent once it already has been set. To fulfill a payment with a different Customer, cancel this PaymentIntent and create a new one.","payment_intent_id":"pi_3RpW0vRFYW7g8VJ20RLD4duz"}
2025-07-27 16:58:53 - customer_created - {"customer_id":"cus_Sl26bxQQ9gJPbs","email":"<EMAIL>"}
2025-07-27 16:58:54 - payment_intent_update_failed - {"error":"You cannot modify `customer` on a PaymentIntent once it already has been set. To fulfill a payment with a different Customer, cancel this PaymentIntent and create a new one.","payment_intent_id":"pi_3RpW0vRFYW7g8VJ20RLD4duz"}
2025-07-27 16:59:41 - payment_intent_created - {"payment_intent_id":"pi_3RpW36RFYW7g8VJ20XcwRAZh","amount":225,"currency":"USD"}
2025-07-27 17:00:12 - customer_created - {"customer_id":"cus_Sl279VXylbrx5r","email":"<EMAIL>"}
2025-07-27 17:00:14 - payment_intent_updated - {"payment_intent_id":"pi_3RpW36RFYW7g8VJ20XcwRAZh","customer_id":"cus_Sl279VXylbrx5r"}
2025-07-27 19:18:37 - payment_intent_created - {"payment_intent_id":"pi_3RpYDYRFYW7g8VJ21pAT9scV","amount":225,"currency":"USD"}
2025-07-27 19:19:17 - customer_created - {"customer_id":"cus_Sl4MVQNZQDtxyr","email":"<EMAIL>"}
2025-07-27 19:19:18 - payment_intent_updated - {"payment_intent_id":"pi_3RpYDYRFYW7g8VJ21pAT9scV","customer_id":"cus_Sl4MVQNZQDtxyr"}
2025-07-27 19:40:27 - payment_intent_created - {"payment_intent_id":"pi_3RpYYfRFYW7g8VJ20uflvLpW","amount":225,"currency":"USD"}
2025-07-27 19:41:42 - payment_intent_created - {"payment_intent_id":"pi_3RpYZtRFYW7g8VJ21Ld0Wu6Y","amount":225,"currency":"USD"}
2025-07-27 19:41:56 - payment_intent_created - {"payment_intent_id":"pi_3RpYa7RFYW7g8VJ21GkGD1Fc","amount":225,"currency":"USD"}
2025-07-27 19:45:45 - payment_intent_created - {"payment_intent_id":"pi_3RpYdoRFYW7g8VJ20Vlb1xda","amount":100,"currency":"usd"}
2025-07-27 19:48:22 - payment_intent_created - {"payment_intent_id":"pi_3RpYgKRFYW7g8VJ21yP907x4","amount":225,"currency":"USD"}
2025-07-27 19:49:39 - payment_intent_created - {"payment_intent_id":"pi_3RpYhaRFYW7g8VJ20caeuEtC","amount":150,"currency":"USD"}
2025-07-27 19:49:56 - payment_intent_created - {"payment_intent_id":"pi_3RpYhrRFYW7g8VJ218wbdaTV","amount":150,"currency":"USD"}
2025-07-27 19:56:10 - payment_intent_created - {"payment_intent_id":"pi_3RpYntRFYW7g8VJ20hEUfNZe","amount":225,"currency":"USD"}
2025-07-27 19:56:24 - payment_intent_created - {"payment_intent_id":"pi_3RpYo7RFYW7g8VJ2104FGcoe","amount":225,"currency":"USD"}
2025-07-27 20:04:02 - payment_intent_created - {"payment_intent_id":"pi_3RpYvVRFYW7g8VJ21XK7536E","amount":75,"currency":"USD"}
2025-07-27 20:04:42 - payment_intent_created - {"payment_intent_id":"pi_3RpYw9RFYW7g8VJ21KQALLCG","amount":75,"currency":"USD"}
2025-07-27 20:05:42 - payment_intent_created - {"payment_intent_id":"pi_3RpYx7RFYW7g8VJ20vnPxNLz","amount":75,"currency":"USD"}
2025-07-27 20:06:08 - payment_intent_created - {"payment_intent_id":"pi_3RpYxXRFYW7g8VJ21R81XU7q","amount":225,"currency":"USD"}
2025-07-27 20:08:36 - payment_intent_created - {"payment_intent_id":"pi_3RpYzvRFYW7g8VJ20khfcrir","amount":450,"currency":"USD"}
2025-07-27 20:09:06 - customer_created - {"customer_id":"cus_Sl5ADu2dQCtvvL","email":"<EMAIL>"}
2025-07-27 20:09:08 - payment_intent_updated - {"payment_intent_id":"pi_3RpYzvRFYW7g8VJ20khfcrir","customer_id":"cus_Sl5ADu2dQCtvvL"}
2025-07-27 20:11:32 - payment_intent_created - {"payment_intent_id":"pi_3RpZ2lRFYW7g8VJ21xU5FR78","amount":225,"currency":"USD"}
2025-07-27 20:11:56 - customer_created - {"customer_id":"cus_Sl5DAnzQYkwHur","email":"<EMAIL>"}
2025-07-27 20:11:57 - payment_intent_updated - {"payment_intent_id":"pi_3RpZ2lRFYW7g8VJ21xU5FR78","customer_id":"cus_Sl5DAnzQYkwHur"}
2025-07-27 20:14:08 - payment_intent_created - {"payment_intent_id":"pi_3RpZ5HRFYW7g8VJ21piugsvH","amount":225,"currency":"USD"}
2025-07-27 20:20:13 - payment_intent_created - {"payment_intent_id":"pi_3RpZBARFYW7g8VJ202IWkLnC","amount":225,"currency":"USD"}
2025-07-27 20:20:37 - customer_created - {"customer_id":"cus_Sl5Mj2Sn3ATEz3","email":"<EMAIL>"}
2025-07-27 20:20:38 - payment_intent_updated - {"payment_intent_id":"pi_3RpZBARFYW7g8VJ202IWkLnC","customer_id":"cus_Sl5Mj2Sn3ATEz3"}
2025-07-27 20:24:36 - payment_intent_created - {"payment_intent_id":"pi_3RpZFORFYW7g8VJ216dotfHs","amount":300,"currency":"USD"}
2025-07-27 20:24:56 - customer_created - {"customer_id":"cus_Sl5QTGyzJSetNW","email":"<EMAIL>"}
2025-07-27 20:24:57 - payment_intent_updated - {"payment_intent_id":"pi_3RpZFORFYW7g8VJ216dotfHs","customer_id":"cus_Sl5QTGyzJSetNW"}
2025-07-27 20:27:38 - payment_intent_created - {"payment_intent_id":"pi_3RpZILRFYW7g8VJ20e4mdvU5","amount":225,"currency":"USD"}
2025-07-27 20:29:22 - payment_intent_created - {"payment_intent_id":"pi_3RpZK1RFYW7g8VJ21klUiOrv","amount":225,"currency":"USD"}
2025-07-27 20:29:41 - customer_created - {"customer_id":"cus_Sl5Vzzivg2ypF6","email":"<EMAIL>"}
2025-07-27 20:29:43 - payment_intent_updated - {"payment_intent_id":"pi_3RpZK1RFYW7g8VJ21klUiOrv","customer_id":"cus_Sl5Vzzivg2ypF6"}
2025-07-27 20:36:58 - payment_intent_created - {"payment_intent_id":"pi_3RpZRNRFYW7g8VJ20ACAhvRp","amount":225,"currency":"USD"}
2025-07-27 20:37:15 - customer_created - {"customer_id":"cus_Sl5cJnkh6n6Gdc","email":"<EMAIL>"}
2025-07-27 20:37:16 - payment_intent_updated - {"payment_intent_id":"pi_3RpZRNRFYW7g8VJ20ACAhvRp","customer_id":"cus_Sl5cJnkh6n6Gdc"}
2025-07-27 20:46:27 - payment_intent_created - {"payment_intent_id":"pi_3RpZaYRFYW7g8VJ21GkYnZMT","amount":300,"currency":"USD"}
2025-07-27 20:46:47 - customer_created - {"customer_id":"cus_Sl5miMNnbriMZt","email":"<EMAIL>"}
2025-07-27 20:46:48 - payment_intent_updated - {"payment_intent_id":"pi_3RpZaYRFYW7g8VJ21GkYnZMT","customer_id":"cus_Sl5miMNnbriMZt"}
2025-07-27 20:59:33 - payment_intent_created - {"payment_intent_id":"pi_3RpZn6RFYW7g8VJ21HMouGRP","amount":225,"currency":"USD"}
2025-07-27 21:00:01 - customer_created - {"customer_id":"cus_Sl5zEyhGa9ZFES","email":"<EMAIL>"}
2025-07-27 21:00:02 - payment_intent_updated - {"payment_intent_id":"pi_3RpZn6RFYW7g8VJ21HMouGRP","customer_id":"cus_Sl5zEyhGa9ZFES"}
2025-07-27 21:08:51 - payment_intent_created - {"payment_intent_id":"pi_3RpZwERFYW7g8VJ20QspM7T5","amount":150,"currency":"USD"}
2025-07-27 21:09:12 - customer_created - {"customer_id":"cus_Sl68FLDlnrUWZG","email":"<EMAIL>"}
2025-07-27 21:09:14 - payment_intent_updated - {"payment_intent_id":"pi_3RpZwERFYW7g8VJ20QspM7T5","customer_id":"cus_Sl68FLDlnrUWZG"}
2025-07-27 21:31:32 - payment_intent_created - {"payment_intent_id":"pi_3RpaIBRFYW7g8VJ20A39fpGh","amount":300,"currency":"USD"}
2025-07-27 21:31:54 - customer_created - {"customer_id":"cus_Sl6V0JXefMo1VU","email":"<EMAIL>"}
2025-07-27 21:31:55 - payment_intent_updated - {"payment_intent_id":"pi_3RpaIBRFYW7g8VJ20A39fpGh","customer_id":"cus_Sl6V0JXefMo1VU"}
2025-07-27 21:47:41 - payment_intent_created - {"payment_intent_id":"pi_3RpaXoRFYW7g8VJ207mY7Vxq","amount":225,"currency":"USD"}
2025-07-27 21:48:05 - customer_created - {"customer_id":"cus_Sl6lYMOep2WPUN","email":"<EMAIL>"}
2025-07-27 21:48:06 - payment_intent_updated - {"payment_intent_id":"pi_3RpaXoRFYW7g8VJ207mY7Vxq","customer_id":"cus_Sl6lYMOep2WPUN"}
2025-07-27 21:58:07 - payment_intent_created - {"payment_intent_id":"pi_3RpahtRFYW7g8VJ21m5hu1eG","amount":150,"currency":"USD"}
2025-07-27 22:03:31 - payment_intent_created - {"payment_intent_id":"pi_3Rpan8RFYW7g8VJ202UuJQ3Q","amount":225,"currency":"USD"}
2025-07-27 22:03:47 - customer_created - {"customer_id":"cus_Sl719Xw2eXLx4H","email":"<EMAIL>"}
2025-07-27 22:03:49 - payment_intent_updated - {"payment_intent_id":"pi_3Rpan8RFYW7g8VJ202UuJQ3Q","customer_id":"cus_Sl719Xw2eXLx4H"}
2025-07-27 22:10:28 - payment_intent_created - {"payment_intent_id":"pi_3RpatrRFYW7g8VJ217Eu4MT1","amount":75,"currency":"USD"}
2025-07-27 22:10:50 - customer_created - {"customer_id":"cus_Sl781gGs2N3W2w","email":"<EMAIL>"}
2025-07-27 22:10:52 - payment_intent_updated - {"payment_intent_id":"pi_3RpatrRFYW7g8VJ217Eu4MT1","customer_id":"cus_Sl781gGs2N3W2w"}
2025-07-27 22:12:09 - payment_intent_created - {"payment_intent_id":"pi_3RpavURFYW7g8VJ20lcI5nPX","amount":75,"currency":"USD"}
2025-07-27 22:12:38 - customer_created - {"customer_id":"cus_Sl7ArhZfmMmcmv","email":"<EMAIL>"}
2025-07-27 22:12:39 - payment_intent_updated - {"payment_intent_id":"pi_3RpavURFYW7g8VJ20lcI5nPX","customer_id":"cus_Sl7ArhZfmMmcmv"}
2025-07-27 22:21:44 - payment_intent_created - {"payment_intent_id":"pi_3Rpb4lRFYW7g8VJ20o5oWWXw","amount":75,"currency":"USD"}
2025-07-27 22:22:02 - customer_created - {"customer_id":"cus_Sl7JrtweNU6SHd","email":"<EMAIL>"}
2025-07-27 22:22:03 - payment_intent_updated - {"payment_intent_id":"pi_3Rpb4lRFYW7g8VJ20o5oWWXw","customer_id":"cus_Sl7JrtweNU6SHd"}
2025-07-27 22:22:19 - customer_created - {"customer_id":"cus_Sl7JMR5KGtjRPI","email":"<EMAIL>"}
2025-07-27 22:22:21 - payment_intent_update_failed - {"error":"You cannot modify `customer` on a PaymentIntent once it already has been set. To fulfill a payment with a different Customer, cancel this PaymentIntent and create a new one.","payment_intent_id":"pi_3Rpb4lRFYW7g8VJ20o5oWWXw"}
2025-07-27 22:22:40 - customer_created - {"customer_id":"cus_Sl7KVaxFrBtJYN","email":"<EMAIL>"}
2025-07-27 22:22:41 - payment_intent_update_failed - {"error":"You cannot modify `customer` on a PaymentIntent once it already has been set. To fulfill a payment with a different Customer, cancel this PaymentIntent and create a new one.","payment_intent_id":"pi_3Rpb4lRFYW7g8VJ20o5oWWXw"}
2025-07-27 22:23:14 - payment_intent_created - {"payment_intent_id":"pi_3Rpb6DRFYW7g8VJ203hgemek","amount":75,"currency":"USD"}
2025-07-27 22:23:32 - customer_created - {"customer_id":"cus_Sl7Leaje2Cj0qt","email":"<EMAIL>"}
2025-07-27 22:23:33 - payment_intent_updated - {"payment_intent_id":"pi_3Rpb6DRFYW7g8VJ203hgemek","customer_id":"cus_Sl7Leaje2Cj0qt"}
2025-07-27 22:46:50 - payment_intent_created - {"payment_intent_id":"pi_3RpbT3RFYW7g8VJ208PpIUE8","amount":75,"currency":"USD"}
2025-07-27 22:47:08 - customer_created - {"customer_id":"cus_Sl7ixZxbFhd5wq","email":"<EMAIL>"}
2025-07-27 22:47:09 - payment_intent_updated - {"payment_intent_id":"pi_3RpbT3RFYW7g8VJ208PpIUE8","customer_id":"cus_Sl7ixZxbFhd5wq"}
2025-07-27 22:50:04 - payment_intent_created - {"payment_intent_id":"pi_3RpbWBRFYW7g8VJ21J9nQZ3V","amount":225,"currency":"USD"}
2025-07-27 22:50:21 - customer_created - {"customer_id":"cus_Sl7mPeEUo8kOKo","email":"<EMAIL>"}
2025-07-27 22:50:22 - payment_intent_updated - {"payment_intent_id":"pi_3RpbWBRFYW7g8VJ21J9nQZ3V","customer_id":"cus_Sl7mPeEUo8kOKo"}
2025-07-27 22:51:30 - payment_intent_created - {"payment_intent_id":"pi_3RpbXZRFYW7g8VJ21kkynduB","amount":75,"currency":"USD"}
2025-07-27 22:51:46 - customer_created - {"customer_id":"cus_Sl7n5KxFXykuO9","email":"<EMAIL>"}
2025-07-27 22:51:47 - payment_intent_updated - {"payment_intent_id":"pi_3RpbXZRFYW7g8VJ21kkynduB","customer_id":"cus_Sl7n5KxFXykuO9"}
2025-07-27 23:03:28 - payment_intent_created - {"payment_intent_id":"pi_3Rpbj8RFYW7g8VJ21j0GA9D2","amount":75,"currency":"USD"}
2025-07-27 23:03:42 - customer_created - {"customer_id":"cus_Sl7zfR6UfdGIG4","email":"<EMAIL>"}
2025-07-27 23:03:44 - payment_intent_updated - {"payment_intent_id":"pi_3Rpbj8RFYW7g8VJ21j0GA9D2","customer_id":"cus_Sl7zfR6UfdGIG4"}
2025-07-27 23:14:49 - payment_intent_created - {"payment_intent_id":"pi_3Rpbu8RFYW7g8VJ20laR9fYg","amount":75,"currency":"USD"}
2025-07-27 23:15:07 - customer_created - {"customer_id":"cus_Sl8ASKGdhZ0Bb0","email":"<EMAIL>"}
2025-07-27 23:15:08 - payment_intent_updated - {"payment_intent_id":"pi_3Rpbu8RFYW7g8VJ20laR9fYg","customer_id":"cus_Sl8ASKGdhZ0Bb0"}
2025-07-27 23:16:51 - payment_intent_created - {"payment_intent_id":"pi_3Rpbw5RFYW7g8VJ21Z54iXsn","amount":75,"currency":"USD"}
2025-07-27 23:17:06 - customer_created - {"customer_id":"cus_Sl8CqY1168IK2H","email":"<EMAIL>"}
2025-07-27 23:17:08 - payment_intent_updated - {"payment_intent_id":"pi_3Rpbw5RFYW7g8VJ21Z54iXsn","customer_id":"cus_Sl8CqY1168IK2H"}
2025-07-27 23:22:18 - payment_intent_created - {"payment_intent_id":"pi_3Rpc1NRFYW7g8VJ217odopHn","amount":75,"currency":"USD"}
2025-07-27 23:22:35 - customer_created - {"customer_id":"cus_Sl8IXnoh9z1QkJ","email":"<EMAIL>"}
2025-07-27 23:22:37 - payment_intent_updated - {"payment_intent_id":"pi_3Rpc1NRFYW7g8VJ217odopHn","customer_id":"cus_Sl8IXnoh9z1QkJ"}
2025-07-27 23:24:23 - payment_intent_created - {"payment_intent_id":"pi_3Rpc3ORFYW7g8VJ219QcBsUE","amount":75,"currency":"USD"}
2025-07-27 23:24:41 - customer_created - {"customer_id":"cus_Sl8K7oWMdtB3vo","email":"<EMAIL>"}
2025-07-27 23:24:42 - payment_intent_updated - {"payment_intent_id":"pi_3Rpc3ORFYW7g8VJ219QcBsUE","customer_id":"cus_Sl8K7oWMdtB3vo"}
2025-07-27 23:27:54 - payment_intent_created - {"payment_intent_id":"pi_3Rpc6nRFYW7g8VJ207imeqdy","amount":75,"currency":"USD"}
2025-07-27 23:28:12 - customer_created - {"customer_id":"cus_Sl8N81mgo8MmUk","email":"<EMAIL>"}
2025-07-27 23:28:13 - payment_intent_updated - {"payment_intent_id":"pi_3Rpc6nRFYW7g8VJ207imeqdy","customer_id":"cus_Sl8N81mgo8MmUk"}
2025-07-27 23:52:10 - payment_intent_created - {"payment_intent_id":"pi_3RpcUHRFYW7g8VJ20fpyUv0Z","amount":75,"currency":"USD"}
2025-07-27 23:52:29 - customer_created - {"customer_id":"cus_Sl8maYV7IakYGV","email":"<EMAIL>"}
2025-07-27 23:52:31 - payment_intent_updated - {"payment_intent_id":"pi_3RpcUHRFYW7g8VJ20fpyUv0Z","customer_id":"cus_Sl8maYV7IakYGV"}
2025-07-28 00:14:11 - payment_intent_created - {"payment_intent_id":"pi_3RpcpaRFYW7g8VJ20gWzWKpP","amount":150,"currency":"USD"}
2025-07-28 00:14:29 - customer_created - {"customer_id":"cus_Sl98TXMl7LUlGj","email":"<EMAIL>"}
2025-07-28 00:14:30 - payment_intent_updated - {"payment_intent_id":"pi_3RpcpaRFYW7g8VJ20gWzWKpP","customer_id":"cus_Sl98TXMl7LUlGj"}
2025-07-28 06:52:20 - payment_intent_created - {"payment_intent_id":"pi_3Rpj2uRFYW7g8VJ20ms3ROf9","amount":75,"currency":"USD"}
2025-07-28 06:53:13 - customer_created - {"customer_id":"cus_SlFYvFvMujTJKD","email":"<EMAIL>"}
2025-07-28 06:53:14 - payment_intent_updated - {"payment_intent_id":"pi_3Rpj2uRFYW7g8VJ20ms3ROf9","customer_id":"cus_SlFYvFvMujTJKD"}
2025-07-28 06:56:22 - payment_intent_created - {"payment_intent_id":"pi_3Rpj6oRFYW7g8VJ20x1Ghd6K","amount":75,"currency":"USD"}
2025-07-28 06:56:26 - customer_created - {"customer_id":"cus_SlFcXb4PFrnCrl","email":"<EMAIL>"}
2025-07-28 06:56:28 - payment_intent_updated - {"payment_intent_id":"pi_3Rpj6oRFYW7g8VJ20x1Ghd6K","customer_id":"cus_SlFcXb4PFrnCrl"}
2025-07-28 06:57:03 - customer_created - {"customer_id":"cus_SlFc0D6QfhftC6","email":"<EMAIL>"}
2025-07-28 06:57:04 - payment_intent_update_failed - {"error":"You cannot modify `customer` on a PaymentIntent once it already has been set. To fulfill a payment with a different Customer, cancel this PaymentIntent and create a new one.","payment_intent_id":"pi_3Rpj6oRFYW7g8VJ20x1Ghd6K"}
2025-07-28 06:57:19 - customer_created - {"customer_id":"cus_SlFcRE9ujJ0LgQ","email":"<EMAIL>"}
2025-07-28 06:57:20 - payment_intent_update_failed - {"error":"You cannot modify `customer` on a PaymentIntent once it already has been set. To fulfill a payment with a different Customer, cancel this PaymentIntent and create a new one.","payment_intent_id":"pi_3Rpj6oRFYW7g8VJ20x1Ghd6K"}
2025-07-28 06:59:05 - payment_intent_created - {"payment_intent_id":"pi_3Rpj9SRFYW7g8VJ21k5DR871","amount":75,"currency":"USD"}
2025-07-28 06:59:37 - customer_created - {"customer_id":"cus_SlFfMsVFs4PWAz","email":"<EMAIL>"}
2025-07-28 06:59:38 - payment_intent_updated - {"payment_intent_id":"pi_3Rpj9SRFYW7g8VJ21k5DR871","customer_id":"cus_SlFfMsVFs4PWAz"}
2025-07-28 07:01:43 - payment_intent_created - {"payment_intent_id":"pi_3RpjBzRFYW7g8VJ21YPaajH5","amount":75,"currency":"USD"}
2025-07-28 07:01:58 - customer_created - {"customer_id":"cus_SlFhMSVaJ8gQll","email":"<EMAIL>"}
2025-07-28 07:01:59 - payment_intent_updated - {"payment_intent_id":"pi_3RpjBzRFYW7g8VJ21YPaajH5","customer_id":"cus_SlFhMSVaJ8gQll"}
2025-07-28 07:03:15 - payment_intent_created - {"payment_intent_id":"pi_3RpjDURFYW7g8VJ20ksgCP5b","amount":225,"currency":"USD"}
2025-07-28 07:03:36 - customer_created - {"customer_id":"cus_SlFjXN60OmXpV8","email":"<EMAIL>"}
2025-07-28 07:03:37 - payment_intent_updated - {"payment_intent_id":"pi_3RpjDURFYW7g8VJ20ksgCP5b","customer_id":"cus_SlFjXN60OmXpV8"}
2025-07-28 07:10:02 - payment_intent_created - {"payment_intent_id":"pi_3RpjK3RFYW7g8VJ21rg6U3OX","amount":75,"currency":"USD"}
2025-07-28 07:10:43 - customer_created - {"customer_id":"cus_SlFqFWFS65XASi","email":"<EMAIL>"}
2025-07-28 07:10:44 - payment_intent_updated - {"payment_intent_id":"pi_3RpjK3RFYW7g8VJ21rg6U3OX","customer_id":"cus_SlFqFWFS65XASi"}
2025-07-28 07:11:49 - payment_intent_created - {"payment_intent_id":"pi_3RpjLmRFYW7g8VJ20FhghLmg","amount":150,"currency":"USD"}
2025-07-28 07:12:19 - customer_created - {"customer_id":"cus_SlFrh2jX7Ukqdj","email":"<EMAIL>"}
2025-07-28 07:12:20 - payment_intent_updated - {"payment_intent_id":"pi_3RpjLmRFYW7g8VJ20FhghLmg","customer_id":"cus_SlFrh2jX7Ukqdj"}
2025-07-28 07:14:11 - payment_intent_created - {"payment_intent_id":"pi_3RpjO3RFYW7g8VJ205EIUjz2","amount":225,"currency":"USD"}
2025-07-28 07:14:35 - customer_created - {"customer_id":"cus_SlFu80pTk1GHPj","email":"<EMAIL>"}
2025-07-28 07:14:36 - payment_intent_updated - {"payment_intent_id":"pi_3RpjO3RFYW7g8VJ205EIUjz2","customer_id":"cus_SlFu80pTk1GHPj"}
2025-07-28 07:16:29 - payment_intent_created - {"payment_intent_id":"pi_3RpjQIRFYW7g8VJ20D0bSnbQ","amount":300,"currency":"USD"}
2025-07-28 07:17:27 - customer_created - {"customer_id":"cus_SlFxVI4qmJVcRI","email":"<EMAIL>"}
2025-07-28 07:17:28 - payment_intent_updated - {"payment_intent_id":"pi_3RpjQIRFYW7g8VJ20D0bSnbQ","customer_id":"cus_SlFxVI4qmJVcRI"}
2025-07-28 07:26:39 - payment_intent_created - {"payment_intent_id":"pi_3Rpja8RFYW7g8VJ20VzYj44t","amount":150,"currency":"USD"}
2025-07-28 07:27:06 - customer_created - {"customer_id":"cus_SlG6fmMF7IxpNh","email":"<EMAIL>"}
2025-07-28 07:27:07 - payment_intent_updated - {"payment_intent_id":"pi_3Rpja8RFYW7g8VJ20VzYj44t","customer_id":"cus_SlG6fmMF7IxpNh"}
2025-07-28 07:28:49 - payment_intent_created - {"payment_intent_id":"pi_3RpjcBRFYW7g8VJ21KVp8p1V","amount":225,"currency":"USD"}
2025-07-28 07:29:25 - customer_created - {"customer_id":"cus_SlG9XDmZaWG9UQ","email":"<EMAIL>"}
2025-07-28 07:29:26 - payment_intent_updated - {"payment_intent_id":"pi_3RpjcBRFYW7g8VJ21KVp8p1V","customer_id":"cus_SlG9XDmZaWG9UQ"}
2025-07-28 07:32:21 - payment_intent_created - {"payment_intent_id":"pi_3RpjfdRFYW7g8VJ21xyOXfas","amount":75,"currency":"USD"}
2025-07-28 07:32:35 - customer_created - {"customer_id":"cus_SlGCVa06VpwXH2","email":"<EMAIL>"}
2025-07-28 07:32:37 - payment_intent_updated - {"payment_intent_id":"pi_3RpjfdRFYW7g8VJ21xyOXfas","customer_id":"cus_SlGCVa06VpwXH2"}
2025-07-28 07:34:32 - payment_intent_created - {"payment_intent_id":"pi_3RpjhkRFYW7g8VJ20cjx9sEy","amount":225,"currency":"USD"}
2025-07-28 07:34:48 - customer_created - {"customer_id":"cus_SlGEYzhkHl1AUz","email":"<EMAIL>"}
2025-07-28 07:34:49 - payment_intent_updated - {"payment_intent_id":"pi_3RpjhkRFYW7g8VJ20cjx9sEy","customer_id":"cus_SlGEYzhkHl1AUz"}
2025-07-28 07:37:46 - payment_intent_created - {"payment_intent_id":"pi_3RpjksRFYW7g8VJ20yWjPpT1","amount":150,"currency":"USD"}
2025-07-28 07:38:22 - customer_created - {"customer_id":"cus_SlGIwLib8i2Opr","email":"<EMAIL>"}
2025-07-28 07:38:24 - payment_intent_updated - {"payment_intent_id":"pi_3RpjksRFYW7g8VJ20yWjPpT1","customer_id":"cus_SlGIwLib8i2Opr"}
2025-07-28 07:40:47 - payment_intent_created - {"payment_intent_id":"pi_3RpjnoRFYW7g8VJ21pJIJNPT","amount":150,"currency":"USD"}
2025-07-28 07:41:05 - customer_created - {"customer_id":"cus_SlGKjcagluhQ0z","email":"<EMAIL>"}
2025-07-28 07:41:07 - payment_intent_updated - {"payment_intent_id":"pi_3RpjnoRFYW7g8VJ21pJIJNPT","customer_id":"cus_SlGKjcagluhQ0z"}
2025-07-28 07:43:06 - payment_intent_created - {"payment_intent_id":"pi_3Rpjq3RFYW7g8VJ20ygguVWs","amount":75,"currency":"USD"}
2025-07-28 07:44:03 - customer_created - {"customer_id":"cus_SlGNxRt7YkkPw9","email":"<EMAIL>"}
2025-07-28 07:44:05 - payment_intent_updated - {"payment_intent_id":"pi_3Rpjq3RFYW7g8VJ20ygguVWs","customer_id":"cus_SlGNxRt7YkkPw9"}
2025-07-28 08:13:33 - payment_intent_created - {"payment_intent_id":"pi_3RpkJVRFYW7g8VJ21XEf6d9S","amount":225,"currency":"USD"}
2025-07-28 08:15:46 - customer_created - {"customer_id":"cus_SlGt3A5jav4Syu","email":"<EMAIL>"}
2025-07-28 08:15:47 - payment_intent_updated - {"payment_intent_id":"pi_3RpkJVRFYW7g8VJ21XEf6d9S","customer_id":"cus_SlGt3A5jav4Syu"}
2025-07-28 08:17:31 - customer_created - {"customer_id":"cus_SlGvQCEfZ75ixr","email":"<EMAIL>"}
2025-07-28 08:17:32 - payment_intent_update_failed - {"error":"You cannot modify `customer` on a PaymentIntent once it already has been set. To fulfill a payment with a different Customer, cancel this PaymentIntent and create a new one.","payment_intent_id":"pi_3RpkJVRFYW7g8VJ21XEf6d9S"}
2025-07-28 08:18:37 - payment_intent_created - {"payment_intent_id":"pi_3RpkOQRFYW7g8VJ20ExYpNZq","amount":225,"currency":"USD"}
2025-07-28 08:19:22 - customer_created - {"customer_id":"cus_SlGxO4RrghIQhX","email":"<EMAIL>"}
2025-07-28 08:19:23 - payment_intent_updated - {"payment_intent_id":"pi_3RpkOQRFYW7g8VJ20ExYpNZq","customer_id":"cus_SlGxO4RrghIQhX"}
2025-07-28 08:21:55 - payment_intent_created - {"payment_intent_id":"pi_3RpkRcRFYW7g8VJ21sIV4SFA","amount":225,"currency":"USD"}
2025-07-28 08:22:34 - customer_created - {"customer_id":"cus_SlH0QJJMXEWdZU","email":"<EMAIL>"}
2025-07-28 08:22:36 - payment_intent_updated - {"payment_intent_id":"pi_3RpkRcRFYW7g8VJ21sIV4SFA","customer_id":"cus_SlH0QJJMXEWdZU"}
2025-07-28 08:24:36 - payment_intent_created - {"payment_intent_id":"pi_3RpkUDRFYW7g8VJ21VPZNbbl","amount":225,"currency":"USD"}
2025-07-28 08:24:54 - customer_created - {"customer_id":"cus_SlH2tQKfjwBI4T","email":"<EMAIL>"}
2025-07-28 08:24:55 - payment_intent_updated - {"payment_intent_id":"pi_3RpkUDRFYW7g8VJ21VPZNbbl","customer_id":"cus_SlH2tQKfjwBI4T"}
2025-07-28 08:27:20 - payment_intent_created - {"payment_intent_id":"pi_3RpkWrRFYW7g8VJ210bNuELU","amount":225,"currency":"USD"}
2025-07-28 08:27:40 - customer_created - {"customer_id":"cus_SlH5rP9jTaBi8K","email":"<EMAIL>"}
2025-07-28 08:27:41 - payment_intent_updated - {"payment_intent_id":"pi_3RpkWrRFYW7g8VJ210bNuELU","customer_id":"cus_SlH5rP9jTaBi8K"}
2025-07-28 08:32:04 - payment_intent_created - {"payment_intent_id":"pi_3RpkbPRFYW7g8VJ20CyF6fpU","amount":225,"currency":"USD"}
2025-07-28 08:32:56 - customer_created - {"customer_id":"cus_SlHAfHtFoIrXY1","email":"<EMAIL>"}
2025-07-28 08:32:57 - payment_intent_updated - {"payment_intent_id":"pi_3RpkbPRFYW7g8VJ20CyF6fpU","customer_id":"cus_SlHAfHtFoIrXY1"}
2025-07-28 08:36:32 - payment_intent_created - {"payment_intent_id":"pi_3RpkfkRFYW7g8VJ204LTkoW7","amount":75,"currency":"USD"}
2025-07-28 08:36:56 - customer_created - {"customer_id":"cus_SlHEjHn9IrRY0v","email":"<EMAIL>"}
2025-07-28 08:36:57 - payment_intent_updated - {"payment_intent_id":"pi_3RpkfkRFYW7g8VJ204LTkoW7","customer_id":"cus_SlHEjHn9IrRY0v"}
2025-07-28 08:39:34 - payment_intent_created - {"payment_intent_id":"pi_3RpkihRFYW7g8VJ219fmcmvg","amount":75,"currency":"USD"}
2025-07-28 08:39:51 - customer_created - {"customer_id":"cus_SlHHUakL4LzSWo","email":"<EMAIL>"}
2025-07-28 08:39:52 - payment_intent_updated - {"payment_intent_id":"pi_3RpkihRFYW7g8VJ219fmcmvg","customer_id":"cus_SlHHUakL4LzSWo"}
2025-07-28 08:45:49 - payment_intent_created - {"payment_intent_id":"pi_3RpkokRFYW7g8VJ204dDTKyJ","amount":300,"currency":"USD"}
2025-07-28 08:46:16 - customer_created - {"customer_id":"cus_SlHNeRbVOdqGNZ","email":"<EMAIL>"}
2025-07-28 08:46:17 - payment_intent_updated - {"payment_intent_id":"pi_3RpkokRFYW7g8VJ204dDTKyJ","customer_id":"cus_SlHNeRbVOdqGNZ"}
2025-07-28 08:58:31 - payment_intent_created - {"payment_intent_id":"pi_3Rpl0xRFYW7g8VJ20D4vmJQW","amount":225,"currency":"USD"}
2025-07-28 08:58:49 - customer_created - {"customer_id":"cus_SlHaqZcpYJpCTt","email":"<EMAIL>"}
2025-07-28 08:58:51 - payment_intent_updated - {"payment_intent_id":"pi_3Rpl0xRFYW7g8VJ20D4vmJQW","customer_id":"cus_SlHaqZcpYJpCTt"}
2025-07-28 09:00:07 - payment_intent_created - {"payment_intent_id":"pi_3Rpl2ZRFYW7g8VJ21HPpGEw5","amount":300,"currency":"USD"}
2025-07-28 09:00:51 - customer_created - {"customer_id":"cus_SlHcQFBygwwdzL","email":"<EMAIL>"}
2025-07-28 09:00:52 - payment_intent_updated - {"payment_intent_id":"pi_3Rpl2ZRFYW7g8VJ21HPpGEw5","customer_id":"cus_SlHcQFBygwwdzL"}
2025-07-28 09:03:10 - payment_intent_created - {"payment_intent_id":"pi_3Rpl5WRFYW7g8VJ21frOgqac","amount":75,"currency":"USD"}
2025-07-28 09:03:52 - customer_created - {"customer_id":"cus_SlHfs4UIiEzC5U","email":"<EMAIL>"}
2025-07-28 09:03:54 - payment_intent_updated - {"payment_intent_id":"pi_3Rpl5WRFYW7g8VJ21frOgqac","customer_id":"cus_SlHfs4UIiEzC5U"}
2025-07-28 09:06:27 - payment_intent_created - {"payment_intent_id":"pi_3Rpl8iRFYW7g8VJ206p30BfV","amount":225,"currency":"USD"}
2025-07-28 09:06:52 - customer_created - {"customer_id":"cus_SlHiobrVNy9Fzq","email":"<EMAIL>"}
2025-07-28 09:06:53 - payment_intent_updated - {"payment_intent_id":"pi_3Rpl8iRFYW7g8VJ206p30BfV","customer_id":"cus_SlHiobrVNy9Fzq"}
2025-07-28 09:14:53 - payment_intent_created - {"payment_intent_id":"pi_3RplGsRFYW7g8VJ20qtMyb2t","amount":225,"currency":"USD"}
2025-07-28 09:15:30 - customer_created - {"customer_id":"cus_SlHrlePysQHy7U","email":"<EMAIL>"}
2025-07-28 09:15:31 - payment_intent_updated - {"payment_intent_id":"pi_3RplGsRFYW7g8VJ20qtMyb2t","customer_id":"cus_SlHrlePysQHy7U"}
2025-07-28 09:27:13 - payment_intent_created - {"payment_intent_id":"pi_3RplSnRFYW7g8VJ204FRvgkA","amount":225,"currency":"USD"}
2025-07-28 09:27:33 - customer_created - {"customer_id":"cus_SlI3ZM7ScYxwO8","email":"<EMAIL>"}
2025-07-28 09:27:34 - payment_intent_updated - {"payment_intent_id":"pi_3RplSnRFYW7g8VJ204FRvgkA","customer_id":"cus_SlI3ZM7ScYxwO8"}
2025-07-28 09:28:46 - payment_intent_created - {"payment_intent_id":"pi_3RplUJRFYW7g8VJ20NdagVt0","amount":225,"currency":"USD"}
2025-07-28 09:29:19 - customer_created - {"customer_id":"cus_SlI5N2hkfb8NCn","email":"<EMAIL>"}
2025-07-28 09:29:21 - payment_intent_updated - {"payment_intent_id":"pi_3RplUJRFYW7g8VJ20NdagVt0","customer_id":"cus_SlI5N2hkfb8NCn"}
2025-07-28 09:31:00 - payment_intent_created - {"payment_intent_id":"pi_3RplWSRFYW7g8VJ21jIbLXzb","amount":225,"currency":"USD"}
2025-07-28 09:31:16 - customer_created - {"customer_id":"cus_SlI6w09y9N6IuF","email":"<EMAIL>"}
2025-07-28 09:31:18 - payment_intent_updated - {"payment_intent_id":"pi_3RplWSRFYW7g8VJ21jIbLXzb","customer_id":"cus_SlI6w09y9N6IuF"}
2025-07-28 09:33:06 - payment_intent_created - {"payment_intent_id":"pi_3RplYURFYW7g8VJ21HRVSqoT","amount":225,"currency":"USD"}
2025-07-28 09:33:44 - customer_created - {"customer_id":"cus_SlI90HQvnhEMn8","email":"<EMAIL>"}
2025-07-28 09:33:46 - payment_intent_updated - {"payment_intent_id":"pi_3RplYURFYW7g8VJ21HRVSqoT","customer_id":"cus_SlI90HQvnhEMn8"}
2025-07-28 09:37:10 - payment_intent_created - {"payment_intent_id":"pi_3RplcRRFYW7g8VJ21TETQjRk","amount":150,"currency":"USD"}
2025-07-28 09:37:27 - customer_created - {"customer_id":"cus_SlIDHEeheDsDnv","email":"<EMAIL>"}
2025-07-28 09:37:28 - payment_intent_updated - {"payment_intent_id":"pi_3RplcRRFYW7g8VJ21TETQjRk","customer_id":"cus_SlIDHEeheDsDnv"}
2025-07-28 09:39:59 - payment_intent_created - {"payment_intent_id":"pi_3Rplf9RFYW7g8VJ21jJdBjzQ","amount":225,"currency":"USD"}
2025-07-28 09:40:44 - customer_created - {"customer_id":"cus_SlIGhwbXnVfMH9","email":"<EMAIL>"}
2025-07-28 09:40:45 - payment_intent_updated - {"payment_intent_id":"pi_3Rplf9RFYW7g8VJ21jJdBjzQ","customer_id":"cus_SlIGhwbXnVfMH9"}
2025-07-28 09:57:37 - payment_intent_created - {"payment_intent_id":"pi_3RplwDRFYW7g8VJ20mS2Kf9p","amount":225,"currency":"USD"}
2025-07-28 09:58:04 - customer_created - {"customer_id":"cus_SlIXSHFdTVFMxy","email":"<EMAIL>"}
2025-07-28 09:58:06 - payment_intent_updated - {"payment_intent_id":"pi_3RplwDRFYW7g8VJ20mS2Kf9p","customer_id":"cus_SlIXSHFdTVFMxy"}
2025-07-28 10:00:35 - payment_intent_created - {"payment_intent_id":"pi_3Rplz6RFYW7g8VJ20FbWg1Ly","amount":75,"currency":"USD"}
2025-07-28 10:00:56 - customer_created - {"customer_id":"cus_SlIav3Ji0WvKeb","email":"<EMAIL>"}
2025-07-28 10:00:57 - payment_intent_updated - {"payment_intent_id":"pi_3Rplz6RFYW7g8VJ20FbWg1Ly","customer_id":"cus_SlIav3Ji0WvKeb"}
2025-07-28 10:02:27 - payment_intent_created - {"payment_intent_id":"pi_3Rpm0tRFYW7g8VJ21pU9xAB7","amount":225,"currency":"USD"}
2025-07-28 10:03:07 - customer_created - {"customer_id":"cus_SlIclJoBf13LSD","email":"<EMAIL>"}
2025-07-28 10:03:09 - payment_intent_updated - {"payment_intent_id":"pi_3Rpm0tRFYW7g8VJ21pU9xAB7","customer_id":"cus_SlIclJoBf13LSD"}
2025-07-28 10:04:19 - payment_intent_created - {"payment_intent_id":"pi_3Rpm2hRFYW7g8VJ21RBV9XWQ","amount":150,"currency":"USD"}
2025-07-28 10:04:30 - customer_created - {"customer_id":"cus_SlIeZRn67zh6J3","email":"<EMAIL>"}
2025-07-28 10:04:32 - payment_intent_updated - {"payment_intent_id":"pi_3Rpm2hRFYW7g8VJ21RBV9XWQ","customer_id":"cus_SlIeZRn67zh6J3"}
2025-07-28 10:15:35 - payment_intent_created - {"payment_intent_id":"pi_3RpmDcRFYW7g8VJ20954r5o5","amount":75,"currency":"USD"}
2025-07-28 10:15:54 - customer_created - {"customer_id":"cus_SlIpN6vUkhrgjc","email":"<EMAIL>"}
2025-07-28 10:15:55 - payment_intent_updated - {"payment_intent_id":"pi_3RpmDcRFYW7g8VJ20954r5o5","customer_id":"cus_SlIpN6vUkhrgjc"}
2025-07-28 10:24:26 - payment_intent_created - {"payment_intent_id":"pi_3RpmMARFYW7g8VJ20H16noIJ","amount":75,"currency":"USD"}
2025-07-28 10:24:49 - customer_created - {"customer_id":"cus_SlIy8MuacPNOu3","email":"<EMAIL>"}
2025-07-28 10:24:51 - payment_intent_updated - {"payment_intent_id":"pi_3RpmMARFYW7g8VJ20H16noIJ","customer_id":"cus_SlIy8MuacPNOu3"}
2025-07-28 10:27:11 - payment_intent_created - {"payment_intent_id":"pi_3RpmOoRFYW7g8VJ21wgGtJjh","amount":75,"currency":"USD"}
2025-07-28 10:31:13 - customer_created - {"customer_id":"cus_SlJ4HyhXRb005o","email":"<EMAIL>"}
2025-07-28 10:31:14 - payment_intent_updated - {"payment_intent_id":"pi_3RpmOoRFYW7g8VJ21wgGtJjh","customer_id":"cus_SlJ4HyhXRb005o"}
2025-07-28 10:34:51 - payment_intent_created - {"payment_intent_id":"pi_3RpmWGRFYW7g8VJ20JjxppKh","amount":75,"currency":"USD"}
2025-07-28 10:35:02 - customer_created - {"customer_id":"cus_SlJ8EVrdEtz8yN","email":"<EMAIL>"}
2025-07-28 10:35:03 - payment_intent_updated - {"payment_intent_id":"pi_3RpmWGRFYW7g8VJ20JjxppKh","customer_id":"cus_SlJ8EVrdEtz8yN"}
2025-07-28 17:57:58 - payment_intent_created - {"payment_intent_id":"pi_3RptR4RFYW7g8VJ20dtIc4qC","amount":75,"currency":"USD"}
2025-07-28 17:58:13 - customer_created - {"customer_id":"cus_SlQHZ8RbsWj6hT","email":"<EMAIL>"}
2025-07-28 17:58:14 - payment_intent_updated - {"payment_intent_id":"pi_3RptR4RFYW7g8VJ20dtIc4qC","customer_id":"cus_SlQHZ8RbsWj6hT"}
2025-07-28 18:15:03 - payment_intent_created - {"payment_intent_id":"pi_3RpthcRFYW7g8VJ20e67FiIq","amount":225,"currency":"USD"}
2025-07-28 18:15:40 - customer_created - {"customer_id":"cus_SlQZuzPNQ0vORh","email":"<EMAIL>"}
2025-07-28 18:15:42 - payment_intent_updated - {"payment_intent_id":"pi_3RpthcRFYW7g8VJ20e67FiIq","customer_id":"cus_SlQZuzPNQ0vORh"}
2025-07-28 18:16:50 - payment_intent_created - {"payment_intent_id":"pi_3RptjKRFYW7g8VJ21nT3FnT2","amount":225,"currency":"USD"}
2025-07-28 18:17:04 - customer_created - {"customer_id":"cus_SlQav9GTNd1g5l","email":"<EMAIL>"}
2025-07-28 18:17:05 - payment_intent_updated - {"payment_intent_id":"pi_3RptjKRFYW7g8VJ21nT3FnT2","customer_id":"cus_SlQav9GTNd1g5l"}
2025-07-28 18:19:54 - payment_intent_created - {"payment_intent_id":"pi_3RptmIRFYW7g8VJ20rBAEZvz","amount":375,"currency":"USD"}
2025-07-28 18:20:06 - customer_created - {"customer_id":"cus_SlQdpPheARx4Ly","email":"<EMAIL>"}
2025-07-28 18:20:07 - payment_intent_updated - {"payment_intent_id":"pi_3RptmIRFYW7g8VJ20rBAEZvz","customer_id":"cus_SlQdpPheARx4Ly"}
2025-07-28 19:18:54 - payment_intent_created - {"payment_intent_id":"pi_3RpuhGRFYW7g8VJ20T9bg7Oa","amount":225,"currency":"USD"}
2025-07-28 19:19:24 - customer_created - {"customer_id":"cus_SlRbc1KM2Qw3Lt","email":"<EMAIL>"}
2025-07-28 19:19:26 - payment_intent_updated - {"payment_intent_id":"pi_3RpuhGRFYW7g8VJ20T9bg7Oa","customer_id":"cus_SlRbc1KM2Qw3Lt"}
2025-07-28 19:31:01 - payment_intent_created - {"payment_intent_id":"pi_3Rput4RFYW7g8VJ215cIqqLg","amount":300,"currency":"USD"}
2025-07-28 19:32:30 - customer_created - {"customer_id":"cus_SlRoVEvaN08gm5","email":"<EMAIL>"}
2025-07-28 19:32:32 - payment_intent_updated - {"payment_intent_id":"pi_3Rput4RFYW7g8VJ215cIqqLg","customer_id":"cus_SlRoVEvaN08gm5"}
2025-07-28 20:00:10 - payment_intent_created - {"payment_intent_id":"pi_3RpvLKRFYW7g8VJ20tw1fBje","amount":150,"currency":"USD"}
2025-07-28 20:00:36 - customer_created - {"customer_id":"cus_SlSGYF43i0mf0m","email":"<EMAIL>"}
2025-07-28 20:00:38 - payment_intent_updated - {"payment_intent_id":"pi_3RpvLKRFYW7g8VJ20tw1fBje","customer_id":"cus_SlSGYF43i0mf0m"}
2025-07-28 20:02:42 - payment_intent_created - {"payment_intent_id":"pi_3RpvNmRFYW7g8VJ20a1BzK4R","amount":75,"currency":"USD"}
2025-07-28 20:03:01 - customer_created - {"customer_id":"cus_SlSI0oG3ixPuS8","email":"<EMAIL>"}
2025-07-28 20:03:03 - payment_intent_updated - {"payment_intent_id":"pi_3RpvNmRFYW7g8VJ20a1BzK4R","customer_id":"cus_SlSI0oG3ixPuS8"}
2025-07-28 20:19:03 - payment_intent_created - {"payment_intent_id":"pi_3RpvdbRFYW7g8VJ218uoEs56","amount":225,"currency":"USD"}
2025-07-28 20:19:15 - customer_created - {"customer_id":"cus_SlSYlkwEkhdUBT","email":"<EMAIL>"}
2025-07-28 20:19:16 - payment_intent_updated - {"payment_intent_id":"pi_3RpvdbRFYW7g8VJ218uoEs56","customer_id":"cus_SlSYlkwEkhdUBT"}
2025-07-28 20:53:17 - payment_intent_created - {"payment_intent_id":"pi_3RpwAiRFYW7g8VJ20OywlFcF","amount":225,"currency":"USD"}
2025-07-28 20:53:38 - customer_created - {"customer_id":"cus_SlT7JZD9z5e5tE","email":"<EMAIL>"}
2025-07-28 20:53:40 - payment_intent_updated - {"payment_intent_id":"pi_3RpwAiRFYW7g8VJ20OywlFcF","customer_id":"cus_SlT7JZD9z5e5tE"}
2025-07-28 21:01:34 - payment_intent_created - {"payment_intent_id":"pi_3RpwIkRFYW7g8VJ20FW42GVI","amount":225,"currency":"USD"}
2025-07-28 21:01:45 - customer_created - {"customer_id":"cus_SlTFedG3nVzGc3","email":"<EMAIL>"}
2025-07-28 21:01:47 - payment_intent_updated - {"payment_intent_id":"pi_3RpwIkRFYW7g8VJ20FW42GVI","customer_id":"cus_SlTFedG3nVzGc3"}
2025-07-28 21:19:43 - payment_intent_created - {"payment_intent_id":"pi_3RpwaJRFYW7g8VJ20RFQn5aT","amount":225,"currency":"USD"}
2025-07-28 21:19:55 - customer_created - {"customer_id":"cus_SlTXDy6r0W1wnI","email":"<EMAIL>"}
2025-07-28 21:19:57 - payment_intent_updated - {"payment_intent_id":"pi_3RpwaJRFYW7g8VJ20RFQn5aT","customer_id":"cus_SlTXDy6r0W1wnI"}
2025-07-28 21:40:19 - payment_intent_created - {"payment_intent_id":"pi_3Rpwu6RFYW7g8VJ20fLYmMfQ","amount":150,"currency":"USD"}
2025-07-28 21:40:36 - customer_created - {"customer_id":"cus_SlTsTApz9CVOQw","email":"<EMAIL>"}
2025-07-28 21:40:37 - payment_intent_updated - {"payment_intent_id":"pi_3Rpwu6RFYW7g8VJ20fLYmMfQ","customer_id":"cus_SlTsTApz9CVOQw"}
2025-07-28 21:41:55 - payment_intent_created - {"payment_intent_id":"pi_3RpwvnRFYW7g8VJ210AyP1Xa","amount":75,"currency":"USD"}
2025-07-28 21:42:13 - customer_created - {"customer_id":"cus_SlTtvBsSUIiTVz","email":"<EMAIL>"}
2025-07-28 21:42:14 - payment_intent_updated - {"payment_intent_id":"pi_3RpwvnRFYW7g8VJ210AyP1Xa","customer_id":"cus_SlTtvBsSUIiTVz"}
2025-07-28 22:13:33 - payment_intent_created - {"payment_intent_id":"pi_3RpxQPRFYW7g8VJ20DcIqgFC","amount":225,"currency":"USD"}
2025-07-28 22:14:04 - customer_created - {"customer_id":"cus_SlUPIJTxG4sDpn","email":"<EMAIL>"}
2025-07-28 22:14:06 - payment_intent_updated - {"payment_intent_id":"pi_3RpxQPRFYW7g8VJ20DcIqgFC","customer_id":"cus_SlUPIJTxG4sDpn"}
2025-07-28 22:19:51 - payment_intent_created - {"payment_intent_id":"pi_3RpxWVRFYW7g8VJ215MfC9IN","amount":300,"currency":"USD"}
2025-07-28 22:20:08 - customer_created - {"customer_id":"cus_SlUVM0ixpkRSzd","email":"<EMAIL>"}
2025-07-28 22:20:10 - payment_intent_updated - {"payment_intent_id":"pi_3RpxWVRFYW7g8VJ215MfC9IN","customer_id":"cus_SlUVM0ixpkRSzd"}
2025-07-29 06:44:53 - payment_intent_created - {"payment_intent_id":"pi_3Rq5PFRFYW7g8VJ21QbR40Ig","amount":75,"currency":"USD"}
2025-07-29 06:45:15 - customer_created - {"customer_id":"cus_Slce7dc2Z2FATV","email":"<EMAIL>"}
2025-07-29 06:45:16 - payment_intent_updated - {"payment_intent_id":"pi_3Rq5PFRFYW7g8VJ21QbR40Ig","customer_id":"cus_Slce7dc2Z2FATV"}
2025-07-29 07:35:15 - payment_intent_created - {"payment_intent_id":"pi_3Rq6C0RFYW7g8VJ21B9SZArF","amount":150,"currency":"USD"}
2025-07-29 07:35:37 - customer_created - {"customer_id":"cus_SldT0Sb7QO0TO0","email":"<EMAIL>"}
2025-07-29 07:35:38 - payment_intent_updated - {"payment_intent_id":"pi_3Rq6C0RFYW7g8VJ21B9SZArF","customer_id":"cus_SldT0Sb7QO0TO0"}
2025-07-29 08:26:39 - payment_intent_created - {"payment_intent_id":"pi_3Rq6zkRFYW7g8VJ21zj3uvCh","amount":150,"currency":"USD"}
2025-07-29 08:26:55 - customer_created - {"customer_id":"cus_SleIhQL51wUzFW","email":"<EMAIL>"}
2025-07-29 08:26:57 - payment_intent_updated - {"payment_intent_id":"pi_3Rq6zkRFYW7g8VJ21zj3uvCh","customer_id":"cus_SleIhQL51wUzFW"}
2025-07-29 09:41:05 - payment_intent_created - {"payment_intent_id":"pi_3Rq89fRFYW7g8VJ21bekeIYB","amount":525,"currency":"USD"}
2025-07-29 09:41:18 - customer_created - {"customer_id":"cus_SlfUDwLkatYZ0N","email":"<EMAIL>"}
2025-07-29 09:41:19 - payment_intent_updated - {"payment_intent_id":"pi_3Rq89fRFYW7g8VJ21bekeIYB","customer_id":"cus_SlfUDwLkatYZ0N"}
2025-07-29 10:00:51 - payment_intent_failed - {"error":"Metadata values can have up to 500 characters, but you passed in a value that is 1015 characters. Invalid value: [\"2025-07-30\",\"2025-07-31\",\"2025-08-01\",\"2025-08-02\",\"2025-08-03\",\"2025-08-04\",\"2025-08-05\",\"2025-08...5-10-08\",\"2025-10-09\",\"2025-10-10\",\"2025-10-11\",\"2025-10-12\",\"2025-10-13\",\"2025-10-14\",\"2025-10-15\"]","amount":5850,"currency":"USD"}
