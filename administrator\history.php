<?php
require_once 'includes/auth.php';
requireAdminLogin();

require_once dirname(__DIR__) . '/config/database.php';

$current_admin = getCurrentAdmin();

// Get search parameter
$search_query = trim($_GET['search'] ?? '');

// Get pagination parameters
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page_options = [10, 25, 50, 100];
$per_page = (int)($_GET['per_page'] ?? 15);
if (!in_array($per_page, $per_page_options)) {
    $per_page = 15; // Default fallback
}
$offset = ($page - 1) * $per_page;

// Build search conditions
$where_conditions = [];
$params = [];

if ($search_query) {
    $where_conditions[] = "(o.order_number LIKE ? OR o.customer_name LIKE ? OR o.title LIKE ? OR oh.notes LIKE ?)";
    $search_param = '%' . $search_query . '%';
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    $pdo = getDBConnection();

    // First, get total count for pagination
    $count_query = "
        SELECT COUNT(*)
        FROM order_history oh
        JOIN orders o ON oh.order_id = o.id
        LEFT JOIN admins a ON oh.changed_by = a.id
        $where_clause
    ";
    $count_stmt = $pdo->prepare($count_query);
    $count_stmt->execute($params);
    $total_records = $count_stmt->fetchColumn();
    $total_pages = ceil($total_records / $per_page);

    // Get order history with order and admin details
    $query = "
        SELECT
            oh.*,
            o.order_number,
            o.customer_name,
            o.title,
            a.username as changed_by_username
        FROM order_history oh
        JOIN orders o ON oh.order_id = o.id
        LEFT JOIN admins a ON oh.changed_by = a.id
        $where_clause
        ORDER BY oh.changed_at DESC
        LIMIT $per_page OFFSET $offset
    ";

    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $history = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error_message = 'Database error occurred.';
    $history = [];
    $total_records = 0;
    $total_pages = 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>History - Borges Media Admin</title>
    <link rel="stylesheet" href="../assets/css/admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-content">
                <div class="header-brand">
                    <img src="../assets/images/bm-header-logo.png" alt="Borges Media Logo" class="admin-logo">
                <h1>Borges Media Billboard Admin</h1>
                </div>
                <div class="header-user">
                    Welcome, <?php echo htmlspecialchars($current_admin['username']); ?>
                </div>
            </div>
        </header>
        
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="orders.php">List of Orders</a></li>
                    <li><a href="history.php" class="active">History</a></li>
                    <li><a href="?logout=1" class="logout-btn">Logout</a></li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <div class="main-content">
                <h2>Order History</h2>
                <div class="history-stats">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <h4><?php echo $search_query ? 'Filtered Results' : 'Total Records'; ?></h4>
                            <div class="stat-number"><?php echo $total_records; ?></div>
                        </div>
                        <div class="stat-card">
                            <h4>Current Page</h4>
                            <div class="stat-number"><?php echo count($history); ?></div>
                            <small>Records shown</small>
                        </div>
                        <?php if ($total_pages > 1): ?>
                        <div class="stat-card">
                            <h4>Total Pages</h4>
                            <div class="stat-number"><?php echo $total_pages; ?></div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-error"><?php echo $error_message; ?></div>
                <?php endif; ?>

                <!-- Search -->
                <div class="filters">
                    <form method="GET" class="filter-form">
                        <div class="filter-group">
                            <label for="search">Search:</label>
                            <input type="text" name="search" id="search" placeholder="Order #, Customer name, Title, or Notes..." value="<?php echo htmlspecialchars($search_query); ?>">
                        </div>

                        <div class="filter-group">
                            <label for="per_page">Show:</label>
                            <select name="per_page" id="per_page">
                                <?php foreach ($per_page_options as $option): ?>
                                    <option value="<?php echo $option; ?>" <?php echo $per_page === $option ? 'selected' : ''; ?>>
                                        <?php echo $option; ?> rows
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Search
                        </button>
                        <a href="history.php" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </form>
                </div>

                <!-- History Table -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <!-- Ill comment some columns for now and uncomment it in the future -->
                                <th>Date/Time</th>
                                <th>Order #</th>
                                <th>Customer</th>
                                <th>Title</th>
                                <!-- <th>Status Change</th> -->
                                <!-- <th>Changed By</th> -->
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($history)): ?>
                                <?php foreach ($history as $record): ?>
                                    <tr>
                                        <!-- Ill comment some columns for now and uncomment it in the future -->
                                        <td><?php echo date('M j, Y g:i A', strtotime($record['changed_at'])); ?></td>
                                        <td><?php echo htmlspecialchars($record['order_number']); ?></td>
                                        <td><?php echo htmlspecialchars($record['customer_name']); ?></td>
                                        <td><?php echo htmlspecialchars($record['title']); ?></td>
                                        <!-- <td>
                                            <?php if ($record['status_from']): ?>
                                                <span class="status status-<?php echo $record['status_from']; ?>">
                                                    <?php echo ucfirst(str_replace('_', ' ', $record['status_from'])); ?>
                                                </span>
                                                →
                                            <?php endif; ?>
                                            <span class="status status-<?php echo $record['status_to']; ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $record['status_to'])); ?>
                                            </span>
                                        </td> -->
                                        <!-- <td><?php echo htmlspecialchars($record['changed_by_username'] ?? 'System'); ?></td> -->
                                        <td><?php echo htmlspecialchars($record['notes'] ?? ''); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center">No history records found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination-container">
                        <div class="pagination-info">
                            Showing <?php echo (($page - 1) * $per_page) + 1; ?> to <?php echo min($page * $per_page, $total_records); ?> of <?php echo $total_records; ?> records (<?php echo $per_page; ?> per page)
                        </div>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" class="pagination-btn">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            <?php endif; ?>

                            <?php
                            // Calculate page range to show
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            // Show first page if not in range
                            if ($start_page > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>" class="pagination-btn">1</a>
                                <?php if ($start_page > 2): ?>
                                    <span class="pagination-dots">...</span>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <?php if ($i == $page): ?>
                                    <span class="pagination-btn active"><?php echo $i; ?></span>
                                <?php else: ?>
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>" class="pagination-btn"><?php echo $i; ?></a>
                                <?php endif; ?>
                            <?php endfor; ?>

                            <?php
                            // Show last page if not in range
                            if ($end_page < $total_pages): ?>
                                <?php if ($end_page < $total_pages - 1): ?>
                                    <span class="pagination-dots">...</span>
                                <?php endif; ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>" class="pagination-btn"><?php echo $total_pages; ?></a>
                            <?php endif; ?>

                            <?php if ($page < $total_pages): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" class="pagination-btn">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>


            </div>
        </main>
    </div>
    
    <script src="../assets/js/admin-script.js"></script>
</body>
</html>
