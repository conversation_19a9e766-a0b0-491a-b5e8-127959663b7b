/* Admin Panel Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* Login Page Styles */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.login-box {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo {
    margin-bottom: 20px;
}

.login-logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border-radius: 12px;
    background: #f8f9fa;
    padding: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.login-header h1 {
    color: #333;
    margin-bottom: 10px;
    font-size: 28px;
}

.login-header p {
    color: #666;
    font-size: 14px;
}

.login-form {
    margin-bottom: 20px;
}

.login-footer {
    text-align: center;
    margin-top: 20px;
}

.register-link {
    text-align: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.register-link p {
    color: #666;
    font-size: 14px;
}

.register-link a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.register-link a:hover {
    text-decoration: underline;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s;
    margin-right: 10px;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Alerts */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
    font-weight: 500;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Admin Layout */
.admin-layout {
    display: grid;
    grid-template-areas:
        "header header"
        "sidebar main";
    grid-template-columns: 250px 1fr;
    grid-template-rows: 60px 1fr;
    min-height: 100vh;
}

.admin-header {
    grid-area: header;
    background: #2c3e50;
    color: white;
    display: flex;
    align-items: center;
    padding: 0 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.header-brand {
    display: flex;
    align-items: center;
    gap: 15px;
}

.admin-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
    border-radius: 6px;
    background: rgba(255,255,255,0.1);
    padding: 3px;
}

.header-content h1 {
    font-size: 20px;
    font-weight: 600;
}

.header-user {
    font-size: 14px;
    color: #bdc3c7;
}

.admin-sidebar {
    grid-area: sidebar;
    background: #34495e;
    padding: 20px 0;
    position: sticky;
    top: 60px;
    height: calc(100vh - 60px);
    overflow-y: auto;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav li {
    margin-bottom: 5px;
}

.sidebar-nav a {
    display: block;
    padding: 12px 20px;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s;
}

.sidebar-nav a:hover,
.sidebar-nav a.active {
    background: #2c3e50;
    color: white;
    border-right: 3px solid #3498db;
}

.logout-btn {
    color: #e74c3c !important;
}

.logout-btn:hover {
    background: #c0392b !important;
    color: white !important;
}

.admin-main {
    grid-area: main;
    padding: 30px;
    overflow-y: auto;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-card h3 {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-number {
    font-size: 36px;
    font-weight: bold;
    color: #2c3e50;
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 1px;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.text-center {
    text-align: center;
}

/* Status and Badge Styles */
.status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-in_progress {
    background: #cce5ff;
    color: #004085;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.badge-templated {
    background: #e7f3ff;
    color: #0066cc;
}

.badge-custom {
    background: #f0e6ff;
    color: #6600cc;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    position: relative;
}

.close {
    position: absolute;
    right: 15px;
    top: 15px;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

/* Filters */
.filters {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.filter-form {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-group label {
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.filter-group input[type="text"] {
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s;
    min-width: 500px;
    width: 100%;
    max-width: 400px;
}

.filter-group input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
}

.filter-group input[type="text"]::placeholder {
    color: #999;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-layout {
        grid-template-areas:
            "header"
            "sidebar"
            "main";
        grid-template-columns: 1fr;
        grid-template-rows: 60px auto 1fr;
    }

    .admin-sidebar {
        padding: 10px 0;
        position: static;
        height: auto;
    }

    .header-brand {
        gap: 10px;
    }

    .admin-logo {
        width: 32px;
        height: 32px;
    }

    .header-content h1 {
        font-size: 18px;
    }

    .login-logo img {
        width: 60px;
        height: 60px;
    }
    
    .sidebar-nav {
        display: flex;
        overflow-x: auto;
    }
    
    .sidebar-nav ul {
        display: flex;
        white-space: nowrap;
    }
    
    .sidebar-nav li {
        margin-right: 5px;
        margin-bottom: 0;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .filter-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .data-table {
        font-size: 12px;
    }
    
    .data-table th,
    .data-table td {
        padding: 8px;
    }

    .pagination-container {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* Calendar Bookings Styles */
.booking-dates {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.booking-count {
    font-weight: 600;
    color: #667eea;
    font-size: 12px;
}

.booking-dates-list {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
}

.booking-date {
    background: #e8f2ff;
    color: #1e40af;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
}

.booking-more {
    background: #f3f4f6;
    color: #6b7280;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-style: italic;
}

.no-bookings {
    color: #9ca3af;
    font-style: italic;
    font-size: 12px;
}

/* Modal Booking Styles */
.modal-booking-info {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.modal-booking-info h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
}

.modal-booking-summary {
    margin-bottom: 15px;
}

.booking-count-large {
    background: #667eea;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
}

.modal-booking-dates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 8px;
}

.modal-booking-date {
    background: #e8f2ff;
    color: #1e40af;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    text-align: center;
    border: 1px solid #bfdbfe;
}

.no-bookings-modal {
    color: #6b7280;
    font-style: italic;
    text-align: center;
    padding: 20px;
    background: #f9fafb;
    border-radius: 6px;
    border: 2px dashed #d1d5db;
}

/* Enhanced Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

/* Enhanced Badge Styles */
.badge-success {
    background: #d4edda;
    color: #155724;
}

.badge-warning {
    background: #fff3cd;
    color: #856404;
}

.badge-info {
    background: #d1ecf1;
    color: #0c5460;
}

/* Enhanced Status Styles */
.status-paid {
    background: #d4edda;
    color: #155724;
}

.status-launched {
    background: #e2e3e5;
    color: #383d41;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #117a8b;
}

/* Image Modal Styles */
.modal-large {
    max-width: 90%;
    width: 800px;
}

#imageContainer {
    text-align: center;
    margin: 20px 0;
    max-height: 70vh;
    overflow: auto;
}

#billboardImage {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.image-actions {
    text-align: center;
    margin-top: 20px;
}

/* Text Utilities */
.text-muted {
    color: #6c757d;
}

/* Table Responsive */
.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #dee2e6;
}

/* Pagination Styles */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.pagination-info {
    color: #666;
    font-size: 14px;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 5px;
}

.pagination-btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    color: #333;
    text-decoration: none;
    border-radius: 5px;
    font-size: 14px;
    transition: all 0.3s;
    min-width: 40px;
    justify-content: center;
}

.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #667eea;
    color: #667eea;
}

.pagination-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.pagination-dots {
    padding: 8px 4px;
    color: #999;
}
