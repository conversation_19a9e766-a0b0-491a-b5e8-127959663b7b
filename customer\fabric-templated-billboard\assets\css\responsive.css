/* ========================================
   RESPONSIVE DESIGN AND MEDIA QUERIES
   ======================================== */

/* Tablet Layout */
@media (min-width: 768px) {
    .main-content {
        flex-direction: row;
        gap: var(--spacing-lg);
    }

    .control-panel {
        width: 300px;
        flex-shrink: 0;
        order: 1;
    }

    .canvas-section {
        flex: 1;
        order: 2;
        position: sticky;
        top: 100px;
        align-self: flex-start;
        z-index: 100;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .properties-panel {
        width: 250px;
        flex-shrink: 0;
        order: 3;
    }

    .template-option {
        /* Tablet sizing - slightly larger templates */
        min-width: 200px;
        max-width: 240px;
        width: 220px;
    }

    .template-preview {
        /* Adjust height for tablet sizing - maintain 16:9 ratio */
        height: 124px; /* 220px * 9/16 ≈ 124px */
        min-height: 124px;
    }

    .canvas-container {
        min-height: 400px;
    }
}

/* Desktop Layout */
@media (min-width: 1024px) {
    .header h1 {
        font-size: 1.5rem;
    }

    .template-option {
        /* Desktop sizing - larger templates for better visibility */
        min-width: 240px;
        max-width: 280px;
        width: 260px;
    }

    .template-preview {
        /* Adjust height for desktop sizing - maintain 16:9 ratio */
        height: 146px; /* 260px * 9/16 ≈ 146px */
        min-height: 146px;
    }

    .canvas-container {
        min-height: 500px;
    }

    .control-panel {
        width: 350px;
    }

    .properties-panel {
        width: 300px;
    }
}

/* Touch Device Optimizations */
.touch-device button {
    min-height: var(--touch-target);
}

.touch-device .template-option {
    /* Touch-friendly sizing - slightly larger for easier tapping */
    min-width: 180px;
    max-width: 220px;
    width: 200px;
}

.touch-device .template-preview {
    /* Maintain 16:9 ratio for touch devices */
    height: 113px; /* 200px * 9/16 ≈ 113px */
    min-height: 113px;
}

/* Keyboard Open State (Mobile) */
.keyboard-open .canvas-section {
    display: none;
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    #billboard-canvas {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Mobile Responsive for Panels */
@media (max-width: 768px) {
    .text-customization-panel, .image-upload-panel {
        position: fixed;
        top: auto;
        left: 0;
        right: 0;
        bottom: 0;
        transform: none;
        width: 100%;
        max-width: none;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        max-height: 85vh;
    }

    .panel-content {
        padding: var(--spacing-md) var(--spacing-md) 0;
        max-height: calc(85vh - 140px);
    }

    /* Single column layout on mobile */
    .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .form-group.full-width {
        grid-column: 1;
    }

    .panel-content label {
        font-size: 0.85rem;
    }

    .panel-content input[type="text"],
    .panel-content input[type="color"],
    .panel-content input[type="range"],
    .panel-content select {
        padding: 12px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .panel-content input[type="color"] {
        height: 50px;
    }

    .text-field-group {
        margin-bottom: var(--spacing-sm);
        padding: var(--spacing-sm);
    }

    /* Mobile responsive for inline customize buttons */
    .text-field-group .customize-btn-inline {
        width: 44px !important;
        height: 44px !important;
        padding: 12px !important;
        font-size: 16px !important;
    }

    .panel-actions {
        padding: var(--spacing-sm) var(--spacing-md) calc(var(--spacing-md) + env(safe-area-inset-bottom));
    }

    /* Mobile shadow controls */
    .shadow-controls {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .toggle-btn {
        width: 36px;
        height: 36px;
    }
}

/* Light Theme Only - Dark Mode Disabled */

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 🔥 NEW: Canvas Transform Scaling Support for Super High Quality Export */
.canvas-container {
    /* Support transform scaling for mobile displays while maintaining original canvas dimensions */
    overflow: visible !important;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.canvas-container canvas {
    /* Allow canvas to be scaled via CSS transform without affecting export quality */
    max-width: none !important;
    max-height: none !important;
    transform-origin: top left;
}

/* Mobile-specific canvas container adjustments */
@media (max-width: 767px) {
    .canvas-container {
        padding: 1rem;
        min-height: 200px; /* Ensure minimum space for scaled canvas */
        /* 🔥 ENHANCED: Ensure proper flexbox centering on mobile */
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        overflow: visible !important;
    }

    /* Ensure canvas wrapper doesn't interfere with transform scaling */
    .canvas-container .upper-canvas {
        position: absolute !important;
    }

    /* 🔥 FIX: Ensure canvas wrapper is properly positioned for flexbox centering */
    .canvas-container .canvas-container,
    .canvas-container canvas,
    .canvas-container .canvas-wrapper {
        position: relative !important;
        margin: 0 !important;
    }
}
