/* ========================================
   LAYOUT AND CONTAINER STYLES
   ======================================== */

/* Container and Layout */
.editor-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    max-width: 100vw;
}

/* Header styles removed - using shared header */

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: var(--spacing-md);
    gap: var(--spacing-md);
}

/* Canvas Container */
.canvas-section {
    background: white;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    box-shadow: var(--shadow);
    order: 2;
}

.canvas-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    background: #fafafa;
    border-radius: var(--border-radius);
    position: relative;
    /* 🔥 ENHANCED: Better canvas centering support */
    overflow: visible;
    width: 100%;
}

#billboard-canvas {
    border: 2px solid #000000; /* black color for the canvas border */
    border-radius: 4px;
    max-width: 100%;
    height: auto;
}

/* Control Panel */
.control-panel {
    background: white;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    box-shadow: var(--shadow);
    order: 1;
}

.control-section {
    margin-bottom: var(--spacing-lg);
}

.control-section:last-child {
    margin-bottom: 0;
}

.control-section h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Properties Panel */
.properties-panel {
    background: white;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    box-shadow: var(--shadow);
    display: none;
    order: 3;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

.action-buttons button {
    flex: 1;
}

/* Checkout Button Styling */
.checkout-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.checkout-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.checkout-btn:active {
    transform: translateY(0);
}

.checkout-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ccc;
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}
