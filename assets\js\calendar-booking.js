// ========================================
// CALENDAR BOOKING SYSTEM
// ========================================

class CalendarBooking {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.currentDate = new Date();
        this.selectedDates = [];
        this.startDate = null;
        this.endDate = null;
        this.isSelectingRange = false;

        // Configuration
        this.options = {
            minDate: new Date(),
            maxDate: new Date(Date.now() + (365 * 24 * 60 * 60 * 1000)), // 1 year from now
            minBookingDays: 1,
            maxBookingDays: 30,
            allowMultipleSelection: false, // Changed to false for range selection
            ...options
        };

        this.init();
    }
    
    init() {
        this.render();
        this.attachEventListeners();
    }
    
    render() {
        const calendarHTML = `
            <div class="calendar-booking">
                <div class="calendar-header">
                    <button type="button" class="calendar-nav-btn" id="prevMonth">&lt;</button>
                    <h3 class="calendar-title" id="calendarTitle"></h3>
                    <button type="button" class="calendar-nav-btn" id="nextMonth">&gt;</button>
                </div>
                <div class="calendar-weekdays">
                    <div class="weekday">Sun</div>
                    <div class="weekday">Mon</div>
                    <div class="weekday">Tue</div>
                    <div class="weekday">Wed</div>
                    <div class="weekday">Thu</div>
                    <div class="weekday">Fri</div>
                    <div class="weekday">Sat</div>
                </div>
                <div class="calendar-days" id="calendarDays"></div>
                <div class="calendar-selection-info">
                    <p id="selectionInfo">Select dates for your billboard display</p>
                    <div class="selected-dates-list" id="selectedDatesList"></div>
                </div>
            </div>
        `;

        this.container.innerHTML = calendarHTML;
        this.updateCalendar();
    }
    
    attachEventListeners() {
        document.getElementById('prevMonth').addEventListener('click', () => {
            this.currentDate.setMonth(this.currentDate.getMonth() - 1);
            this.updateCalendar();
        });
        
        document.getElementById('nextMonth').addEventListener('click', () => {
            this.currentDate.setMonth(this.currentDate.getMonth() + 1);
            this.updateCalendar();
        });
        
        document.getElementById('calendarDays').addEventListener('click', (e) => {
            // Allow clicking on all calendar days except those that are truly disabled (outside date range)
            if (e.target.classList.contains('calendar-day') && !e.target.classList.contains('disabled')) {
                this.handleDateClick(e.target);
            }
        });
    }
    
    updateCalendar() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        
        // Update title
        const monthNames = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];
        document.getElementById('calendarTitle').textContent = `${monthNames[month]} ${year}`;
        
        // Generate calendar days
        const firstDay = new Date(year, month, 1);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());
        
        const daysContainer = document.getElementById('calendarDays');
        daysContainer.innerHTML = '';
        
        for (let i = 0; i < 42; i++) {
            const date = new Date(startDate);
            date.setDate(startDate.getDate() + i);
            
            const dayElement = this.createDayElement(date, month);
            daysContainer.appendChild(dayElement);
        }
        
        this.updateSelectionInfo();
    }
    
    createDayElement(date, currentMonth) {
        const dayElement = document.createElement('div');
        dayElement.className = 'calendar-day';
        dayElement.textContent = date.getDate();
        dayElement.dataset.date = this.formatDate(date);

        // Add classes based on date status
        if (date.getMonth() !== currentMonth) {
            dayElement.classList.add('other-month');
        }

        if (this.isDateBeforeMin(date) || this.isDateAfterMax(date)) {
            dayElement.classList.add('disabled');
        }

        if (this.isDateSelected(date)) {
            dayElement.classList.add('selected');

            // Add specific range classes for styling
            if (this.isSelectingRange && this.startDate && this.endDate) {
                if (this.formatDate(date) === this.formatDate(this.startDate)) {
                    dayElement.classList.add('range-start');
                } else if (this.formatDate(date) === this.formatDate(this.endDate)) {
                    dayElement.classList.add('range-end');
                } else {
                    dayElement.classList.add('range-middle');
                }
            } else if (this.startDate && this.formatDate(date) === this.formatDate(this.startDate)) {
                dayElement.classList.add('single-selected');
            }
        }

        if (this.isToday(date)) {
            dayElement.classList.add('today');
        }

        return dayElement;
    }
    
    handleDateClick(dayElement) {
        const dateStr = dayElement.dataset.date;
        const date = new Date(dateStr);

        console.log('Date clicked:', dateStr, 'Current state:', {
            startDate: this.startDate ? this.formatDate(this.startDate) : null,
            endDate: this.endDate ? this.formatDate(this.endDate) : null,
            isSelectingRange: this.isSelectingRange,
            selectedDatesCount: this.selectedDates.length
        });

        // Only prevent clicking on dates that are outside the valid date range
        if (dayElement.classList.contains('disabled')) {
            return;
        }

        // If clicking on an already selected date, clear the selection
        if (this.isDateSelected(date)) {
            console.log('Clearing selection - clicked on selected date');
            this.clearSelection();
            return;
        }

        // Range selection logic
        if (!this.startDate) {
            // First click - set start date
            console.log('First click - setting start date');
            this.startDate = new Date(date);
            this.endDate = null;
            this.isSelectingRange = false;
            this.selectedDates = [new Date(date)];
        } else if (!this.endDate) {
            // Second click - set end date and create range
            console.log('Second click - setting end date and creating range');
            this.endDate = new Date(date);

            // Ensure start date is before end date
            if (this.endDate < this.startDate) {
                console.log('Swapping dates - end date was before start date');
                [this.startDate, this.endDate] = [this.endDate, this.startDate];
            }

            // Calculate the range
            const range = this.calculateDateRange(this.startDate, this.endDate);
            console.log('Calculated range:', range.length, 'days');

            // Check if range exceeds maximum booking days
            if (range.length > this.options.maxBookingDays) {
                alert(`You can select a maximum of ${this.options.maxBookingDays} days. Your selection spans ${range.length} days.`);
                return;
            }

            this.selectedDates = range;
            this.isSelectingRange = true;
        } else {
            // Third click - start new selection
            console.log('Third click - starting new selection');
            this.startDate = new Date(date);
            this.endDate = null;
            this.isSelectingRange = false;
            this.selectedDates = [new Date(date)];
        }

        this.updateCalendar();
        this.updateSelectionInfo();
        this.notifySelectionChange();
    }
    
    addSelectedDate(date) {
        this.selectedDates.push(new Date(date));
        this.selectedDates.sort((a, b) => a - b);
    }
    
    removeSelectedDate(date) {
        this.selectedDates = this.selectedDates.filter(d => 
            this.formatDate(d) !== this.formatDate(date)
        );
    }
    
    isDateSelected(date) {
        return this.selectedDates.some(d => 
            this.formatDate(d) === this.formatDate(date)
        );
    }

    isDateBeforeMin(date) {
        return date < this.options.minDate;
    }
    
    isDateAfterMax(date) {
        return date > this.options.maxDate;
    }
    
    isToday(date) {
        const today = new Date();
        return this.formatDate(date) === this.formatDate(today);
    }
    
    formatDate(date) {
        return date.toISOString().split('T')[0];
    }

    calculateDateRange(startDate, endDate) {
        const dates = [];
        const currentDate = new Date(startDate);

        while (currentDate <= endDate) {
            dates.push(new Date(currentDate));
            currentDate.setDate(currentDate.getDate() + 1);
        }

        return dates;
    }
    
    updateSelectionInfo() {
        const infoElement = document.getElementById('selectionInfo');
        const listElement = document.getElementById('selectedDatesList');

        if (this.selectedDates.length === 0) {
            infoElement.textContent = 'Select dates for your billboard display';
            listElement.innerHTML = '';
        } else if (this.selectedDates.length === 1) {
            infoElement.textContent = '1 day selected';
            const options = { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' };
            listElement.innerHTML = `<span class="selected-date-item">${this.selectedDates[0].toLocaleDateString('en-US', options)}</span>`;
        } else if (this.isSelectingRange && this.startDate && this.endDate) {
            const options = { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' };
            const startDateStr = this.startDate.toLocaleDateString('en-US', options);
            const endDateStr = this.endDate.toLocaleDateString('en-US', options);

            infoElement.textContent = `${this.selectedDates.length} days selected (${startDateStr} - ${endDateStr})`;

            // Show only start and end dates in the list for cleaner display
            listElement.innerHTML = `
                <span class="selected-date-item range-start-item">${startDateStr}</span>
                <span class="range-connector">to</span>
                <span class="selected-date-item range-end-item">${endDateStr}</span>
            `;
        } else {
            infoElement.textContent = `${this.selectedDates.length} day(s) selected`;

            const datesList = this.selectedDates.map(date => {
                const options = { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' };
                return `<span class="selected-date-item">${date.toLocaleDateString('en-US', options)}</span>`;
            }).join('');

            listElement.innerHTML = datesList;
        }
    }

    notifySelectionChange() {
        // Dispatch custom event for other components to listen to
        const event = new CustomEvent('calendarSelectionChange', {
            detail: {
                selectedDates: this.selectedDates.map(d => this.formatDate(d)),
                isValid: this.isSelectionValid()
            }
        });
        document.dispatchEvent(event);
    }
    
    isSelectionValid() {
        return this.selectedDates.length >= this.options.minBookingDays;
    }
    
    getSelectedDates() {
        return this.selectedDates.map(d => this.formatDate(d));
    }
    
    clearSelection() {
        this.selectedDates = [];
        this.startDate = null;
        this.endDate = null;
        this.isSelectingRange = false;
        this.updateCalendar();
        this.notifySelectionChange();
    }
}

// Global calendar instance
let billboardCalendar = null;

// Initialize calendar when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const calendarContainer = document.getElementById('billboardCalendar');
    if (calendarContainer) {
        billboardCalendar = new CalendarBooking('billboardCalendar', {
            minBookingDays: 1,
            maxBookingDays: 10000 // Practically unlimited
        });
    }
});

// Export for use in other scripts
window.CalendarBooking = CalendarBooking;
