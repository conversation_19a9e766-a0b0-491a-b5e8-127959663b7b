<?php
// Email Delivery System for Billboard Orders

require_once dirname(__DIR__, 2) . '/config/database.php';
require_once dirname(__DIR__, 2) . '/config/email.php';
require_once dirname(__DIR__, 2) . '/vendor/autoload.php'; // Include Composer autoloader
require_once 'image-generator.php';

use P<PERSON>Mailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class BillboardEmailDelivery {
    private $pdo;
    private $order;
    private $mailer;
    
    public function __construct($orderId) {
        $this->pdo = getDBConnection();
        $this->loadOrder($orderId);
        $this->initializeMailer();
    }
    
    private function loadOrder($orderId) {
        $stmt = $this->pdo->prepare("
            SELECT o.*, bi.image_path, bi.image_filename
            FROM orders o
            LEFT JOIN billboard_images bi ON o.id = bi.order_id
            WHERE o.id = ?
        ");
        $stmt->execute([$orderId]);
        $this->order = $stmt->fetch();
        
        if (!$this->order) {
            throw new Exception("Order not found: $orderId");
        }
    }
    
    private function initializeMailer() {
        $this->mailer = new PHPMailer(true);
        
        // Server settings
        $this->mailer->isSMTP();
        $this->mailer->Host = SMTP_HOST;
        $this->mailer->SMTPAuth = true;
        $this->mailer->Username = SMTP_USERNAME;
        $this->mailer->Password = SMTP_PASSWORD;
        $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $this->mailer->Port = SMTP_PORT;
        
        // Default sender
        $this->mailer->setFrom(SMTP_FROM_EMAIL, SMTP_FROM_NAME);
    }
    
    /**
     * Send billboard delivery email with image attachment
     */
    public function sendBillboardDeliveryEmail() {
        try {
            // Generate image if not exists
            if (!$this->order['image_path'] || !file_exists($this->order['image_path'])) {
                $generator = new BillboardImageGenerator($this->order['id']);
                $result = $generator->generateImage();
                
                if (!$result['success']) {
                    throw new Exception('Failed to generate billboard image: ' . $result['error']);
                }
                
                // Reload order to get updated image path
                $this->loadOrder($this->order['id']);
            }
            
            // Prepare email
            $this->mailer->clearAddresses();
            $this->mailer->clearAttachments();
            
            $this->mailer->addAddress($this->order['customer_email'], $this->order['customer_name']);

            // Attach logo for CID embedding
            $logoPath = dirname(__DIR__, 2) . '/assets/images/bm-header-logo.png';
            if (file_exists($logoPath)) {
                $this->mailer->addEmbeddedImage($logoPath, 'logo', 'bm-header-logo.png');
            }

            // Email content
            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Billboard Design Ready - Order #' . $this->order['order_number'];
            $this->mailer->Body = $this->getBillboardDeliveryEmailTemplate();
            $this->mailer->AltBody = 'Your billboard design for Order #' . $this->order['order_number'] . ' is ready. Please check your email for the attached design file.';
            
            // Attach billboard image
            if ($this->order['image_path'] && file_exists($this->order['image_path'])) {
                $this->mailer->addAttachment(
                    $this->order['image_path'],
                    $this->order['image_filename'] ?: 'billboard-design.png'
                );
            }
            
            // Send email
            $sent = $this->mailer->send();
            
            if ($sent) {
                $this->logEmailDelivery('billboard_delivery', 'sent');
                $this->updateOrderEmailStatus('sent');
                
                return [
                    'success' => true,
                    'message' => 'Billboard delivery email sent successfully'
                ];
            } else {
                throw new Exception('Failed to send email');
            }
            
        } catch (Exception $e) {
            $this->logEmailDelivery('billboard_delivery', 'failed', $e->getMessage());
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Send launch notification email
     */
    public function sendLaunchNotificationEmail() {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->clearAttachments();
            
            $this->mailer->addAddress($this->order['customer_email'], $this->order['customer_name']);

            // Attach logo for CID embedding
            $logoPath = dirname(__DIR__, 2) . '/assets/images/bm-header-logo.png';
            if (file_exists($logoPath)) {
                $this->mailer->addEmbeddedImage($logoPath, 'logo', 'bm-header-logo.png');
            }

            // Email content
            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Billboard Now Live - Order #' . $this->order['order_number'];
            $this->mailer->Body = $this->getLaunchNotificationEmailTemplate();
            $this->mailer->AltBody = 'Your billboard for Order #' . $this->order['order_number'] . ' is now live and being displayed.';
            
            // Send email
            $sent = $this->mailer->send();
            
            if ($sent) {
                $this->logEmailDelivery('launch_notification', 'sent');
                
                return [
                    'success' => true,
                    'message' => 'Launch notification email sent successfully'
                ];
            } else {
                throw new Exception('Failed to send launch notification');
            }
            
        } catch (Exception $e) {
            $this->logEmailDelivery('launch_notification', 'failed', $e->getMessage());
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get billboard delivery email template
     */
    private function getBillboardDeliveryEmailTemplate() {
        $customerName = htmlspecialchars($this->order['customer_name']);
        $orderNumber = htmlspecialchars($this->order['order_number']);
        $billboardType = ucfirst($this->order['billboard_type']);
        
        $displayDates = '';
        if ($this->order['booking_start_date']) {
            $startDate = date('F j, Y', strtotime($this->order['booking_start_date']));
            $endDate = date('F j, Y', strtotime($this->order['booking_end_date']));
            
            if ($this->order['booking_start_date'] === $this->order['booking_end_date']) {
                $displayDates = $startDate;
            } else {
                $displayDates = $startDate . ' - ' . $endDate;
            }
        }
        
        return "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Your Billboard Design</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .header-logo { margin-bottom: 20px; text-align: center; }
        .header-logo img { width: 80px; height: 80px; object-fit: contain; border-radius: 12px; background: rgba(255,255,255,0.2); padding: 8px; display: block; border: 0; outline: none; text-decoration: none; margin: 0 auto; }
        .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
        .order-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e9ecef; }
        .detail-row:last-child { border-bottom: none; }
        .footer { text-align: center; margin-top: 30px; color: #6c757d; font-size: 0.9rem; }
        .btn { display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <div class='header-logo'>
                <img src='cid:logo' alt='Borges Media Logo' width='80' height='80' style='width: 80px; height: 80px; object-fit: contain; border-radius: 12px; background: rgba(255,255,255,0.2); padding: 8px; display: block; border: 0; outline: none; text-decoration: none; margin: 0 auto;'>
            </div>
            <h1>Your Billboard Design is Ready!</h1>
            <p>Thank you for choosing Borges Media</p>
        </div>

        <div class='content'>
            <p>Dear {$customerName},</p>

            <p>Great news! Your billboard design has been completed and is attached to this email. We're excited to help you share your message with the world!</p>

            <div class='order-details'>
                <h3>Order Details</h3>
                <div class='detail-row'>
                    <span><strong>Order Number:</strong></span>
                    <span>{$orderNumber}</span>
                </div>
                <div class='detail-row'>
                    <span><strong>Billboard Type:</strong></span>
                    <span>{$billboardType}</span>
                </div>
                <div class='detail-row'>
                    <span><strong>Display Period:</strong></span>
                    <span>{$displayDates}</span>
                </div>
                <div class='detail-row'>
                    <span><strong>Total Amount:</strong></span>
                    <span>\$" . number_format($this->order['total_amount'], 2) . "</span>
                </div>
            </div>

            <h3>What Happens Next?</h3>
            <ul>
                <li><strong>Review:</strong> Our team will review your design for final approval</li>
                <li><strong>Preparation:</strong> We'll prepare your billboard for display</li>
                <li><strong>Launch:</strong> Your billboard will go live on your selected dates</li>
                <li><strong>Notification:</strong> You'll receive an email when your billboard is launched</li>
            </ul>

            <p><strong>Your billboard design is attached to this email.</strong> Please save it for your records.</p>

            <p>If you have any questions or need to make changes, please contact us immediately at <a href='mailto:<EMAIL>'><EMAIL></a>.</p>

            <p>Thank you for trusting us with your billboard advertising needs!</p>

            <p>Best regards,<br>
            <strong>The Borges Media Team</strong></p>
        </div>

        <div class='footer'>
            <p>© " . date('Y') . " Borges Media. All rights reserved.</p>
            <p>This email was sent regarding your billboard order #{$orderNumber}</p>
        </div>
    </div>
</body>
</html>";
    }
    
    /**
     * Get launch notification email template
     */
    private function getLaunchNotificationEmailTemplate() {
        $customerName = htmlspecialchars($this->order['customer_name']);
        $orderNumber = htmlspecialchars($this->order['order_number']);
        
        return "<!DOCTYPE html>
                <html>
                <head>
                    <meta charset='UTF-8'>
                    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                    <title>Your Billboard is Live!</title>
                    <style>
                        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                        .header { background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
                        .header-logo { margin-bottom: 20px; text-align: center; }
                        .header-logo img { width: 80px; height: 80px; object-fit: contain; border-radius: 12px; background: rgba(255,255,255,0.2); padding: 8px; display: block; border: 0; outline: none; text-decoration: none; margin: 0 auto; }
                        .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
                        .highlight { background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 20px 0; border-radius: 0 6px 6px 0; }
                        .footer { text-align: center; margin-top: 30px; color: #6c757d; font-size: 0.9rem; }
                    </style>
                </head>
                <body>
                    <div class='container'>
                        <div class='header'>
                            <div class='header-logo'>
                                <img src='cid:logo' alt='Borges Media Logo' width='80' height='80' style='width: 80px; height: 80px; object-fit: contain; border-radius: 12px; background: rgba(255,255,255,0.2); padding: 8px; display: block; border: 0; outline: none; text-decoration: none; margin: 0 auto;'>
                            </div>
                            <h1>Your Billboard is Now Live!</h1>
                            <p>Your billboard is now being displayed</p>
                        </div>

                        <div class='content'>
                            <p>Dear {$customerName},</p>

                            <p>Exciting news! Your billboard (Order #{$orderNumber}) has been successfully launched and is now being displayed to thousands of viewers.</p>

                            <div class='highlight'>
                                <h3>🎯 Your Billboard is Making an Impact!</h3>
                                <p>Your billboard is now visible to our audience and working to achieve your goals. Thank you for choosing Borges Media for your advertising needs.</p>
                            </div>

                            <h3>📞 Need Support?</h3>
                            <p>If you have any questions about your billboard display or need assistance with future campaigns, our team is here to help:</p>
                            <ul>
                                <li>Email: <a href='mailto:<EMAIL>'><EMAIL></a></li>
                                <li>Phone: (*************</li>
                            </ul>

                            <p>We hope your billboard campaign is successful and look forward to working with you again!</p>

                            <p>Best regards,<br>
                            <strong>The Borges Media Team</strong></p>
                        </div>

                        <div class='footer'>
                            <p>© " . date('Y') . " Borges Media. All rights reserved.</p>
                            <p>This email was sent regarding your billboard order #{$orderNumber}</p>
                        </div>
                    </div>
                </body>
                </html>";
    }
    
    /**
     * Log email delivery attempt
     */
    private function logEmailDelivery($emailType, $status, $errorMessage = null) {
        $stmt = $this->pdo->prepare("
            INSERT INTO email_delivery_logs (
                order_id, email_type, recipient_email, delivery_status,
                delivery_attempts, last_attempt_at, error_message
            ) VALUES (?, ?, ?, ?, 1, NOW(), ?)
            ON DUPLICATE KEY UPDATE
                delivery_attempts = delivery_attempts + 1,
                last_attempt_at = NOW(),
                delivery_status = VALUES(delivery_status),
                error_message = VALUES(error_message)
        ");
        
        $stmt->execute([
            $this->order['id'],
            $emailType,
            $this->order['customer_email'],
            $status,
            $errorMessage
        ]);
    }
    
    /**
     * Update order email status
     */
    private function updateOrderEmailStatus($status) {
        $stmt = $this->pdo->prepare("
            UPDATE orders 
            SET email_sent_at = NOW(), email_delivery_status = ?
            WHERE id = ?
        ");
        $stmt->execute([$status, $this->order['id']]);
    }


}

/**
 * Send billboard delivery email (can be called from external scripts)
 */
function sendBillboardDeliveryEmail($orderId) {
    try {
        $emailDelivery = new BillboardEmailDelivery($orderId);
        return $emailDelivery->sendBillboardDeliveryEmail();
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Send launch notification email (can be called from external scripts)
 */
function sendLaunchNotificationEmail($orderId) {
    try {
        $emailDelivery = new BillboardEmailDelivery($orderId);
        return $emailDelivery->sendLaunchNotificationEmail();
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}
?>
